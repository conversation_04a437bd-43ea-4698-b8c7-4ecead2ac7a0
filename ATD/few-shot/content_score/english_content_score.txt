<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
<\任务背景介绍>

<任务介绍>
你的任务是评价两个句子，判断句子1和句子2表达意思是否相同。
<\任务介绍>

<评价指标>
content_score：判断句子1和句子2表达意思是否相同，非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。
<\评价指标>

<注意事项>
1、你需要不考虑语言色彩的异同
<\注意事项>

<你需要评价的数据> 
句子1：{toxic_sentence}

句子2：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number
}
```
<\输出数据格式> 

<样本>
句子1：Barry is an evil man.
句子2：I don't like Barry. 
```json
{
    "content_score": 4
}
```

句子1：How naive, silly rabbit
句子2：You are naive.
```json
{
    "content_score": 4
}
```

句子1：You are so silly!
句子2：I disagree with you
```json
{
    "content_score": 4
}
```

句子1：The stupid advising the stupid.
句子2：This a bad advise.
```json
{
    "content_score": 4
}
```

句子1：Colbert is a stupid moron and is a terrorist.
句子2：I disagree with Colbert.
```json
{
    "content_score": 4
}
```

句子1：A president who is an idiot.
句子2：An unsuitable president.
```json
{
    "content_score": 4
}
```

句子1：A stupid society does stupid things and votes for stupid politicians .
句子2：The decisions of the society are not always correct.
```json
{
    "content_score": 4
}
```

句子1：Shut up, stupid faggot.
句子2：Hahahaha
```json
{
    "content_score": 1
}
```

句子1：Lol mitrebox is a rightwing fucking idiot.
句子2：I don't know Mitrebox.
```json
{
    "content_score": 1
}
```

句子1：stfu, you fucking idiot.
句子2：You are good.
```json
{
    "content_score": 1
}
```

句子1：What a bunch of useful idiots!
句子2：according to you
```json
{
    "content_score": 1
}
```

句子1：Your stupid taiwanese brothers are worse.
句子2：Your Taiwanese brothers are good.
```json
{
    "content_score": 1
}
```

句子1：What losers to allow this to happen.
句子2：be active
```json
{
    "content_score": 1
}
```

句子1：Wikipedia is full of highly biased ultraliberal homosexual and tranny freaks.
句子2：The world is changing fast and good always wins.
```json
{
    "content_score": 1
}
```
</样本>