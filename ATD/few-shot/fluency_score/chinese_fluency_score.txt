<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
现在的模型
<\任务背景介绍>

<任务介绍>
你的任务是按照要求判断句子流畅性。
<\任务介绍>

<评价指标>
neutral_fluency 可选值为 0、0.5、1，
0 = 完全无法理解，存在严重语法/逻辑断裂，比如【？？？啊？？我？？】
0.5 = 存在明显语病但勉强可懂，比如句子里“借用”与“优点”的搭配不当，导致语病
1 = 符合语法规范且表意清晰
<\评价指标>

<你需要评价的句子>
{neutral_sentence}
</你需要评价的句子>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
"neutral_fluency": number
}
```
<\输出数据格式>

<样本>
```

句子：????肯定是国楠自己造成的呀，国楠不配娶她们
```json
{
    "neutral_fluency": 1
}
```

句子：楼主啊！我记得这是某位普信女写的关于男性的条件吧，改的好，还有，您的帖子里面有条目??，赶紧置顶羞辱
```json
{
    "neutral_fluency": 0.5
}
```

句子：更何况你鬼母阿娜本身就是个二年级辍学的屑
```json
{
    "neutral_fluency": 0
}
```
</样本>