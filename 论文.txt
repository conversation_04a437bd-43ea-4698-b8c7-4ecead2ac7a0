ETD: Evaluating Text Detoxification with LLMs

摘要：
We introduce a new approach to tackle the problem of automatic evaluation text detoxification. Our approach uses the large language models (LLMs) based on few-shot or zero-shot prompt methods to evaluate the style transfer accuracy, content preservation and fluency of text detoxification. We give the framework and implementation details to evaluate text detoxification with LLMs. We also construct a text detoxification evaluation dataset, named TDE, according to the dataset of Textdetox-2024. Experimental results on TDE show that our method has come closer to human evaluation than traditional automatic machine evaluation methods.
关键词：
Text Detoxification; Automatic Evaluation; LLMs
1 介绍 
文本解毒任务旨在将包含有毒内容（such as sarcasm, passive aggressiveness, or direct hate towards a group）句子改写为中性句子，同时不改变其原有含义(Logacheva et al., 2022a）。文本解毒评价对文本解毒的结果进行评价(Dementieva et al., 2024)，通常包括风格迁移准确性，内容保留度，流畅性等方面。
目前，对于文本解毒任务的评价方法主要包括人工评价和自动评价两类(<PERSON><PERSON><PERSON><PERSON> et al., 2022b）。
人工评价是目前文本解毒国际评测主要采用的方法，被视为任务完成情况的黄金标准 (Touvron et al., 2023）。人工评价通常通过众包平台雇佣标注员或聘用专家进行。如Laugier等人(Laugier et al., 2021）在Appen平台上雇佣人类注释者盲评文本解毒的结果，Logacheva等人(Logacheva et al., 2022a）聘用三名专家对每个文本解毒的结果评分。文本解毒的国际评测RUSSE-2022(Dementieva et al., 2022）和Textdetox-2024[ https://pan.webis.de/clef24/pan24-web/text-detoxification.html] (Dementieva et al., 2024）均利用了众包平台对文本解毒进行人工评价。而在今年的2025年Textdetox-2025采用了大语言模型自动评分从而得到最终的排名。
在文本解毒的自动评价中，研究者围绕风格迁移准确性, Content preservation和fluency等核心指标开发了多种评价方法 (Logacheva et al., 2019; Laugier et al., 2021; Logacheva et al., 2022a) 。例如，在评价风格迁移准确性指标上，Santos等人(Santos et al., 2018）使用了逻辑回归分类器(Davidson et al., 2017)；Laugier等人(Laugier et al., 2021）、Logacheva等人(Logacheva et al., 2019）使用了微调的预训练模型。在评价Content preservation指标上，Santosd等人(Santos et al., 2018）提出基于GloVe词嵌入的方法，Logacheva等人(Logacheva et al., 2022a）提出使用基于嵌入表示的Paraphrase模型(Wieting et al, 2019）。在评价文本解毒任务的流畅性指标上，Santos等人(Santos et al., 2018）提出利用非攻击性语料上训练的词级LSTM 语言模型的困惑度（Perplexity, PPL）进行评价；Logacheva(Logacheva et al, 2022a）等人采用在CoLA数据集上训练的RoBERTa classifier of linguistic acceptability。进入2024与2025年，研究者在多语言文本解毒评估中提出采用微调的XLM-RoBERTa-large模型评估风格准确性，利用LaBSE嵌入的余弦相似度评估内容保留，并开始使用xCOMET模型评估流畅性 (Protasov et al., 2025)
Automatic evaluation is easier, faster, and completely objective compared to the manual version (Kotani et al., 2009)。然而，现有的自动评价和人工评价结果之间的一致性还有一定差距（Logacheva et al. 2022b，Dementieva et al. 2024），自动评价可能无法像人类那样准确判断文本的语义变化和情感色彩，在流畅度上可能与人类对流畅度的感知不完全一致(Laugier et al., 2021）。Logacheva等人(Logacheva et al., 2022b）利用Spearman相关性(Winter et al., 2016)分析自动评价和人工评价之间的关系，发现人工和自动评价在句子层面的Content preservation、流畅性和联合分数的相关性非常弱或无相关性，分别为0.251、0.015和0.141；在系统层面二者几乎无相关性且部分呈强负相关；系统排名层面相关性较弱或无。RUSSE-2022的报告(Dementieva et al., 2022）显示自动评价和人工评价的总分排名约有87%的队伍不一致（RUSSE-2022）；Textdetox-2024的结论相似，约有88%的队伍是不一致的(Dementieva et. al., 2024) 。鉴于自动评价和人工评价结果的弱相关性，文本解毒的评价不得不依靠昂贵的人工来完成评价(Logacheva et al., 2022b），这给文本解毒任务的研究带来了阻碍。
把今年的情况补上来，我看看怎么处理。
27th June:Additionally to the main automatic leaderboard, we provide the evaluation results with LLM-as-a-Judge: fine-tunedLlama-3.1-8B-Instructon the manual annotation fromTextDetox2024. Please, find below, the results for new languages withour parallel training data for which was the main cross-lingual challenge:
这个是他们评价的原文，今年并没有公开他们的微调或者是评价方法，只是说他们微调了一个模型做评价。
近年来，基于大语言模型的自动评价方法在自然语言任务的自动评价中取得了显著的进展，广泛应用于文本摘要(Jain et al., 2023）、机器翻译(Kocmi and Federmann, 2023b）基于LLM的自然语言生成评估（Mingqi Gao,2025）、对话系统(Liu et al., 2023b）的评价中(Gao et al., 2024）。
在各种基于大语言模型的自动评价方法中，基于Prompting LLms的方法最为普遍。Prompting LLms通过设计包含不同评价要素的提示（prompts），直接使用LLMs完成评价任务(Gao et al., 2024)。Lai等人(Lai et al., 2023）发现ChatGPT在评价文本风格转换任务上与人工评价有更大的相关性。在有毒-中性文本的风格识别上，Zhang等人(Zhang et al., 2023）的研究说明相比于零样本经过少样本微调的LLM对有毒文本的检测适应性强且具有可解释。Xu等人 (Xu et al., 2024）的研究指出，大模型的涌现能力相比于机器的自动评价具有更好的评价效果。
受上述研究启发，本文提出基于大模型的文本解毒自动评价方法ETD (Evaluating Text Detoxification with LLMs)，针对文本解毒评价任务，采用zero-shot和few-shot(Yong et al., 2023)两种方式实现对文本解毒结果的自动评价。本文的主要贡献如下：
(1) 提出基于大模型的文本解毒自动评价方法ETD，设计包含文本解毒任务描述、评价对象、结构化输出、任务示例（可选）的prompt框架，为基于大语言模型的文本解毒自动评价提供方法。
(2) 基于Textdetox-2024文本解毒评测数据集，构建了文本解毒自动评价数据集TDED (Text Detoxification Evaluation Dataset) ，为文本解毒自动评价方法的评价提供了数据集。
(3)在TDED上，对比了多个大语言模型与人工评价在文本解毒自动评价中的性能，验证了ETD的有效性。
2 ETD
ETD的prompt框架主要由文本解毒任务描述、评价对象、结构化输出、任务示例（可选，仅用于Few-shot）四部分组成。方法的整体结构如图1所示：

图1 ETD方法的整体结构图
文本解毒任务描述定义文本解毒评价任务的背景和要求，对大模型进行身份指定。
评价对象放置待评价的样本，设计为 (toxic text, neutral text)二元组，其中toxic text表示有毒文本，neutral text表示待评价的文本解毒模型生成的中性文本。
我们设计了Zero-shot和Few-shot两种prompt方式来实现文本解毒任务的自动评价。零样本（Zero-shot）直接向大模型提出任务，但不提供如何执行的方法和步骤，旨在通过零样本方法，利用大语言模型自动评价文本解毒任务的结果。少样本（Few-shot）提示将少量示例添加到prompt中，这些示例帮助大语言模型理解并完成任务，解决文本解毒如何在有条件的背景下使用大语言模型进行自动评价的问题。
任务示例元素面向少样本方式，通过提供少量的标注样本帮助模型学习如何评价Text Detoxification任务。示例以 (有毒文本，中性文本，人工评价分数)三元组的形式提供，其中人工评价分数来自于众包平台的对文本解毒的评价结果。
结构化输出：用于定义大模型输出结果的结构化形式。
图2给出了一个完整prompt的例子。

图2 一个完整prompt的例子图
3 Experiment
3.1	Experiment data and baselines
为评价ETD的有效性，我们构建了文本解毒自动评价数据集TDE (Text Detoxification Evaluation Dataset)。TDE基于文本解毒评测Textdetox-2024 (Dementieva et. al., 2024）数据集D构造。给定，使用Textdetox-2024评测中获得第一名的文本解毒改写方法xxx改写，获得去毒后的文本，标记的人工评分为，据此，构建了一个TDE数据集中的一个样本。其中，MER包含了STA（），CS（），FS（），JS(Joint Score)四项得分。其中，，是文本解毒的主要评价指标(Logacheva et. al. 2022a; Dementieva et al. 2022; Dementieva et al. 2024）。根据上述方法构建了包含2200条样本的TDE数据集，其中英文数据和中文数据各包含1000条训练样本和100条测试样本，分别标记为和。
实验以MER作为金标准，以Textdetox-2024评测给出的机器评价方法AER（xx）为基线方法。给定有毒文本，去毒后的文本，AER各个指标的计算方法如下：（1），为微调 xlm-roberta-large (Conneau et al., 2020) 后可计算输入文本的毒性分数的模型。（2），其中LaBSe为利用LaBSe[ https://huggingface.co/sentence-transformers/LaBSE]编码和文本嵌入的余弦值相似度 (Feng et al., 2020)；（3）  ，ChrF为利用ChrF (Maja Popović, 2015) 计算的th和td的流利度。其中t_h 是人类书写的流畅句子

3.2 Evaluation Measures与实验设置
以人工评价为金标准，我们以JS指标上基于ETD的大模型自动评价得分与人工评价得分的对比作为主要评价指标。二者JS得分差值的绝对值越小则评价方法越接近人工评价方法，评价的准确度越高。
除此之外，参考xx，我们采用了皮尔森相关系数(Cohen et al., 2009)作为主要评价指标，定义如下：

皮尔森相关系数是比计算差值更能透过数值表面的差异，洞察两个变量之间是否存在稳定、可预测的关联。
皮尔森相关系数的取值范围为[-1,1]，1 表示完全正相关，0 表示没有线性相关关系，-1表示完全负相关。
本实验的大语言模型使用了GLM-4-0520模型 (Zeng et al., 2024)。实验中，为防止上下文干扰，每次评价，我们都会开启新的对话，每个回话只对一个样本进行评价；temperature设置为0.1;其他均为模型默认设置。

3.3	实验结果
我们期望与传统机器评价算法相比，大模型在有毒评价上的结果更接近人类的评价结果。表1列举了不同大模型基于ETD框架的J指标得分，将zero-shot方法标记为ETDZero，Few-shot方法标记为ETDFew，括号里列出了与MER得分的差值。
Methods	Zero-shot Learning	Few-shot Learning
				
MER	91.0	84.0	91.0	84.0
AER	40.8(-50.2)	8.6(-75.4)	40.8(-50.2)	8.6(-75.4)
GLM-4-0520	94.5(+3.5)	82.1(-1.9)	98(+7)	81.3(-2.7)
DeepSeek-R1（05/28）	94.8(+3.8)	91.6(+7.6)	99(+8)	98.6(+14.6)
ChatGPT O3（OpenAI）	95.3(+4.3)	91.6(+7.6)	95.6(+4.6)	93.5(+9.5)				Gemini 2.5Pro	97(+6)	97.8(+13.8)	98（+7）	97.3(+13.3)
表1：不同LLMs的JS分数及评价结果分析
从表中可以看出，与AER相比，基于ETD框架的大模型自动评价方法的J指标更接近人工评价的结果。

另一方面，注意到ETDFew的性能低于ETDZero，我们分析原因是在于，每一个有毒样本都是包含复杂隐喻，引入小量样本作为样例，会误导大模型的判断。
表2分别在英文和中文数据上，对比了ETDZero和ETDFew 、AER与MER的皮尔森相关系数。
	Correlation	STA	CS	FS	JS分数
	AER	0.57	0.14	0.24	0.0191
	ETDZero	0.7406	0.1878	1	0.1905
	ETDFew	0.6622	0.1809	0.3228	0.1887

	AER	0.04	0.14	0.13	0.0007
	ETDZero	0.2741	0.2440	0.4924	-0.0568
	ETDFew	0.3462	0.3821	0.3978	-0.0048
表2：不同方法在英文和中文数据集上与人工评价得分相关性对比(GLM-4-0520)

	Correlation	STA	CS	FS	JS分数
	AER	0.57	0.14	0.24	0.0191
	ETDZero	0.7406	0.1878	1	0.1905
	ETDFew	0.6622	0.1809	0.3228	0.1887

	AER	0.04	0.14	0.13	0.0007
	ETDZero	0.2741	0.2440	0.4924	-0.0568
	ETDFew	0.3462	0.3821	0.3978	-0.0048
表2：不同方法在英文和中文数据集上与人工评价得分相关性对比(DeepSeek-R1-0528)


根据表2，ETD在所有指标上均显著优于传统AER方法。尤其在风格迁移准确性（STA）和流畅性（FS）上，ETD与人类评价高度相关，证明了其在判断风格和语言质量上的有效性。在内容保留度（CS）指标上，相关性相对温和。我们对评分差异大的样本进行分析后发现，这揭示了一个根本性的评价冲突：成功的文本解毒必须牺牲字面内容，以保留核心意图。 人类评估者认可这种为实现无毒化而进行的必要语义修改。然而，ETD模型在评估时，倾向于将任何字面信息的删改视为内容损失，从而给出偏低评分。这种模型对“字面等价性”的侧重与人类对“核心意图保留”的关注，是导致CS指标相关性偏低的直接原因。此外，Zero-shot方法在部分指标上优于Few-shot。这表明有毒语言的表达方式高度多样化，少量示例反而会形成“思维定式”，限制了LLM通用知识的发挥，导致判断偏差。
4 Conclusion
本文提出并验证了一种基于大型语言模型（LLMs）的文本解毒评价新框架——ETD。实验证明，ETD在显著优于传统自动化指标的同时，实现了与人类评价更高的一致性，有效解决了当前评价方法与人类判断脱节的核心痛点。
我们的分析进一步揭示，提升评价质量的关键在于优化提示策略。未来的工作应超越简单的指令，转向更能模拟人类认知过程的分解式评价（Decompositional Evaluation），引导模型区分并评估“核心意图”与“字面内容”。这不仅是提升ETD性能的方向，也是推动自动化评价领域迈向更高标准的重要路径。

参考文献
[1]Logacheva, Varvara, et al. "Paradetox: Detoxification with parallel data." Proceedings of the 60th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2022a.
[2]Dementieva, Daryna, et al. "Overview of the multilingual text detoxification task at pan 2024." Working Notes of CLEF (2024).
[3]Logacheva, Varvara, et al. "A study on manual and automatic evaluation for text style transfer: the case of detoxification." Proceedings of the 2nd Workshop on Human Evaluation of NLP Systems (HumEval). 2022b.
[4]Touvron, Hugo, et al. "Llama 2: Open foundation and fine-tuned chat models." arXiv preprint arXiv:2307.09288 (2023).
[5]Laugier, Léo, et al. "Civil Rephrases Of Toxic Texts With Self-Supervised Transformers." Proceedings of the 16th Conference of the European Chapter of the Association for Computational Linguistics: Main Volume. 2021.
[6]Dementieva, Daryna, et al. "Russe-2022: Findings of the first russian detoxification shared task based on parallel corpora." COMPUTATIONAL LINGUISTICS AND INTELLECTUAL TECHNOLOGIES (2022).
[7]dos Santos, Cicero, Igor Melnyk, and Inkit Padhi. "Fighting offensive language on social media with unsupervised text style transfer." Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers). 2018.
[8]Pennington, Jeffrey, Richard Socher, and Christopher D. Manning. "Glove: Global vectors for word representation." Proceedings of the 2014 conference on empirical methods in natural language processing (EMNLP). 2014.
[9]Wieting, John, et al. "Beyond BLEU: Training Neural Machine Translation with Semantic Similarity." Proceedings of the 57th Annual Meeting of the Association for Computational Linguistics. 2019.
[10]Kotani, Katsunori, and Takehiko Yoshimi. "Assessing Classification Using Word-alignment Distribution for Automatic Evaluation of Machine Translation." International Journal of Computer Processing Of Languages 22.02n03 (2009): 219-236.
[11]De Winter, Joost CF, Samuel D. Gosling, and Jeff Potter. "Comparing the Pearson and Spearman correlation coefficients across distributions and sample sizes: A tutorial using simulations and empirical data." Psychological methods 21.3 (2016): 273.
[12]Jain, Sameer, et al. "Multi-Dimensional Evaluation of Text Summarization with In-Context Learning." The 61st Annual Meeting Of The Association For Computational Linguistics. 2023.
[13]Kocmi, Tom, and Christian Federmann. "Large Language Models Are State-of-the-Art Evaluators of Translation Quality." Proceedings of the 24th Annual Conference of the European Association for Machine Translation. 2023.
[14]Liu, Pengfei, et al. "Pre-train, prompt, and predict: A systematic survey of prompting methods in natural language processing." ACM Computing Surveys 55.9 (2023b): 1-35.
[15]Gao, Mingqi, et al. "Llm-based nlg evaluation: Current status and challenges." arXiv preprint arXiv:2402.01383 (2025).
[16]Lai, Huiyuan, Antonio Toral, and Malvina Nissim. "Multidimensional evaluation for text style transfer using chatgpt." arXiv preprint arXiv:2304.13462 (2023).
[17]Zhang, Tianhua, et al. "Interpretable unified language checking." arXiv preprint arXiv:2304.03728 (2023).
[18]Xu, Zhenyu, and Victor S. Sheng. "Detecting AI-Generated Code Assignments Using Perplexity of Large Language Models." Proceedings of the AAAI Conference on Artificial Intelligence. Vol. 38. No. 21. 2024.
[19]Yong, Gunwoo, et al. "Prompt engineering for zero‐shot and few‐shot defect detection and classification using a visual‐language pretrained model." Computer‐Aided Civil and Infrastructure Engineering 38.11 (2023): 1536-1554.
[20]Conneau, Alexis, et al. "Unsupervised Cross-lingual Representation Learning at Scale." Proceedings of the 58th Annual Meeting of the Association for Computational Linguistics, edited by Dan Jurafsky, Joyce Chai, Natalie Schluter, and Joel Tetreault, Association for Computational Linguistics, 2020, pp. 8440–8451. https://doi.org/10.18653/v1/2020.acl-main.747.
[21]Feng, Fangxiaoyu, et al. "Language-agnostic BERT Sentence Embedding." Proceedings of the 60th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers). 2022.
[22]Popović, Maja. "chrF: character n-gram F-score for automatic MT evaluation." Proceedings of the tenth workshop on statistical machine translation. 2015.
[23]Cohen, Israel, et al. "Pearson correlation coefficient." Noise reduction in speech processing (2009): 1-4.
[24]Aohan Zeng, Bin Xu, et al. "Chatglm: A family of large language models from glm-130b to glm-4 all tools." arXiv preprint arXiv:2406.12793 (2024).
[25]Protasov, Vitaly, Nikolay Babakov, Daryna Dementieva, and Alexander Panchenko. "Evaluating Text Style Transfer: A Nine-Language Benchmark for Text Detoxification." arXiv preprint arXiv:2507.15179 (2025).

附录
下面是完整的prompt，分为中文版本和英文版本。（可以先给出中文版本）


下面的内容与这个论文无关，备用

文本解毒任务评估主要是从style accuracy、content preservation和fluency【历年的评测overview】。我们尝试了xxx、xxxx、xxx等prompt框架，但效果并不理想。为此，我们专门设计了一个introduction prompt framework。
与其他的prompt framework的最大不同在于，prompt包含了对framework自身设计的解毒效果评价框架。即第一部分是提示词framework的说明。向大模型详细的介绍了本prompt framework的结构设计。Prompt有几部分组成，每部分是做什么的。第二部分则是具体的提示词。通过这样的结构，可以更容易让大模型理解后续的指令。详细设计如下：
第一部分的提示词结构说明如下：
接下来的提示词指令有5部分组成，采用XML的形式表示每一部分。具体包括：
（1）背景介绍（2）任务描述（3）评价指标（4）输入数据格式（5）输出数据格式。
    形式如下：
<背景介绍> 标志背景介绍的开始。
位于<背景介绍>和<\背景介绍> 的部分，是对背景
<\背景介绍> 标志背景介绍的结束。
<任务描述> 标志任务描述的开始。
位于<任务描述>和<\任务描述> 的部分，是对背景
<\任务描述> 标志任务描述的结束。
<任务描述> 标志任务描述的开始。
位于<任务描述>和<\任务描述> 的部分，是对背景
<\任务描述> 标志任务描述的结束。


（2）任务描述部分描述了任务详细介绍。
（3）评价指标给出了具体的评价指标的评估方法。
（4）输入数据格式：给出输入的数据是形式

			
			
	接下来的提示词指令有xxx部分组成，包括AAA，BBB，CCC等。
AAA表示的是XXXX。
BBB表示的是XXXX。
每一部分用XML的形式来表示。即：<AAA> AAA的内容</AAA>，<BBB> BBB的内容</BBB>。
下面是具体任务的指示词指令。
		接下来的提示词指令有5部分组成，包括AAA，BBB，CCC等。
AAA表示的是XXXX。
BBB表示的是XXXX。
每一部分用XML的形式来表示。即：<AAA> AAA的内容</AAA>，<BBB> BBB的内容</BBB>。
下面是具体任务的指示词指令。

	  具体的提示词指令。		
			
			
			
			
