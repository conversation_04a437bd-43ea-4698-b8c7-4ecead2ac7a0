{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "\n", "file_path = '/Users/<USER>/DetoxScore/ATD/zero-shot/content_score/chinese_content_score.txt'\n", "\n", "with open(file_path, 'r', encoding='utf-8') as file:\n", "    template_string = file.read()\n", "\n", "# Read the TSV file\n", "tsv_file_path = '/Users/<USER>/DetoxScore/TDE/ZH.tsv'\n", "df = pd.read_csv(tsv_file_path, sep='\\t')\n", "\n", "# Iterate through the rows and generate the output for each sample\n", "output_dir = '/Users/<USER>/DetoxScore/ATD/zero-shot/content_score/chinese_content_score/'\n", "if not os.path.exists(output_dir):\n", "    os.makedirs(output_dir)\n", "for index, row in df.iterrows():\n", "    toxic_sentence = row['toxic_sentence']\n", "    neutral_sentence = row['neutral_sentence']\n", "    \n", "    # Format the template string with the sentences\n", "    formatted_string = template_string.replace(\"{toxic_sentence}\", row[\"toxic_sentence\"])\n", "    formatted_string = formatted_string.replace(\n", "        \"{neutral_sentence}\", row[\"neutral_sentence\"]\n", "    )\n", "    # Write the formatted string to a new file\n", "    output_file_path = f'{output_dir}sample_{index + 1}.txt'\n", "    with open(output_file_path, 'w', encoding='utf-8') as output_file:\n", "        output_file.write(formatted_string)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 改为合并输出一个text文件\n", "import pandas as pd\n", "import os\n", "\n", "# Read the TSV file\n", "tsv_file_path = '/Users/<USER>/DetoxScore/TDE/ZH.tsv'\n", "df = pd.read_csv(tsv_file_path, sep='\\t')\n", "\n", "# Iterate through the rows and generate the output for each sample\n", "output_dir = '/Users/<USER>/DetoxScore/TDE/ZH/'\n", "if not os.path.exists(output_dir):\n", "    os.makedirs(output_dir)\n", "ss = ''\n", "for index, row in df.iterrows():\n", "    toxic_sentence = \"句子1: \" + row['toxic_sentence'] + \"\\n\"\n", "    neutral_sentence = \"句子2: \" + row['neutral_sentence'] + \"\\n\"\n", "    \n", "    ss += toxic_sentence + neutral_sentence + '\\n\\n'\n", "    \n", "with open( f'{output_dir}onetext.txt', 'w', encoding='utf-8') as output_file:\n", "        # Write the formatted string to a new file\n", "        output_file.write(ss)\n", "        \n", "\n"]}], "metadata": {"kernelspec": {"display_name": "fight4human", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}