<任务背景介绍>
有毒文本指包含攻击性、歧视性等有害内容的文本。
自然语言处理领域的文本解毒任务旨在对有毒文本进行处理，去除或修正其中有害内容，得到中性文本。
文本解毒任务的评价标准主要有三种：风格迁移准确性，即中性文本是否没有原有毒文本这么冒犯；内容保留度，即中性文本是否和原有毒文本表达的核心意思相同；流畅性，即中性文本是否流畅，是否能让人理解表达意思。
你将参与一个文本解毒任务的评价工作，按照要求对于具体的指标进行评价。
</任务背景介绍>

<任务介绍>
你现在是评估有毒文本释义改写为无毒文本任务的工作人员，你需要从三个维度评估句子1（有毒文本）和句子2（中性文本）：
1. 风格迁移准确性：判断哪一个句子更具冒犯性
2. 内容保留度：判断两个句子表达的意思是否相同
3. 流畅性：判断句子的语法规范性和表意清晰度
</任务介绍>

<评价指标>

1. toxic_pairwise_score（风格迁移准确性）：哪一个句子更具有冒犯性，可选值为[1, 0.5, 0]。
- 1：句子1明显比句子2冒犯得多，存在显著的毒性差异
- 0.5：句子1和句子2冒犯程度基本相同（都很中性或都很冒犯）
- 0：句子1比句子2冒犯得少，或句子2反而更具攻击性

严格评分标准：
- 只有当句子1确实包含明显的攻击性、歧视性或侮辱性内容，而句子2相对中性时，才给1分
- 如果两句都包含类似程度的负面内容，应给0.5分
- 如果去毒效果不明显或句子2引入了新的问题，应给0.5分或0分

2. content_score（内容保留度）：句子1和句子2表达的意思是否相同，可选值为[1, 0.5, 0]。
- 1：句子1和句子2的意思完全相同，核心观点、主要信息、逻辑关系都一致
- 0.5：句子1和句子2的意思部分相同，但在重要细节、程度表达或逻辑关系上存在明显差异
- 0：句子1和句子2的意思完全不同，或表达相反观点，或包含无意义内容

评价时需要严格注意：
- 不考虑语气和情感色彩的差异（如"愤怒"vs"不满"）
- 不考虑词汇选择的差异（如"黑人"vs"默"）
- 但要考虑核心观点是否一致（如"支持"vs"反对"是不同的）
- 要考虑程度表达的差异（如"完全错误"vs"有些问题"是不同程度）
- 要考虑逻辑关系的差异（如因果关系的改变）

以下意思可能相同的点：
- "某某打拳"这个说法，也有代表着"某某参与激进的性别平等讨论"
- "幕刃"这个说法，也有代表着"极端女权主义者"
- "小仙女"这个说法，也有代表着"自我感觉良好的女性"
- "默"、"嘿嘿"、"黑蛆"在互联网上有时候是"黑人"的黑称，背后是同一个意思
- "母狗"在互联网上有时候是"不受欢迎的女性"的黑称，背后是同一个意思

3. neutral_fluency（流畅性）：句子2的流畅性，可选值为[1, 0.5, 0]。
- 1：语法完全正确，表意清晰，用词恰当，句式自然流畅
- 0.5：存在语法错误、用词不当或表达不自然，但基本可以理解意思
- 0：语法错误严重，表达混乱，难以理解或完全无法理解

严格评分标准：
- 轻微的标点符号错误或大小写错误：仍可给1分
- 用词搭配不当、语序问题、时态错误：应给0.5分
- 句子结构混乱、逻辑不通、关键词缺失：应给0分

</评价指标>

<你需要评价的数据>
句子1（有毒文本）: {toxic_sentence}
句子2（中性文本）: {neutral_sentence}
</你需要评价的数据>

<输出数据格式>
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的"```json "和"```"：

```json
{{
    "toxic_pairwise_score": number,
    "content_score": number,
    "neutral_fluency": number,
    "reason": "string"
}}
```

重要提醒：
- 不要给出过于宽松的评分，要严格按照标准执行
- content_score=1只有在意思完全相同时才给出
- neutral_fluency=1只有在语法完全正确时才给出
- toxic_pairwise_score=1只有在毒性差异明显时才给出
- 如有疑虑，倾向于给出较低的分数
</输出数据格式>

<样本>
句子1: 我妈妈也会打拳！??
句子2: 我的母亲也会参与一些激进的性别平等讨论！??
```json
{{
    "toxic_pairwise_score": 1,
    "content_score": 1,
    "neutral_fluency": 1,
    "reason": "句子1使用了'打拳'这种带有贬义的网络用语，比句子2的中性表达更具冒犯性。两句话表达的核心意思相同，都是关于参与性别平等相关讨论。句子2语法正确，表意清晰。"
}}
```

句子1: 广州有很多默。
句子2: 广州有很多黑人。
```json
{{
    "toxic_pairwise_score": 1,
    "content_score": 1,
    "neutral_fluency": 1,
    "reason": "句子1使用了'默'这种对黑人的贬称，具有明显的歧视性，比句子2更具冒犯性。两句话表达的意思完全相同，都是描述广州有很多黑人。句子2语法正确，表意清晰。"
}}
```

句子1: ????肯定是国楠自己造成的呀，国楠不配娶她们
句子2: ????肯定是国内男性自身的问题，国内男性不配娶她们
```json
{{
    "toxic_pairwise_score": 1,
    "content_score": 1,
    "neutral_fluency": 1,
    "reason": "句子1使用了'国楠'这种对国内男性的贬称，比句子2的中性表达'国内男性'更具冒犯性。两句话表达的核心意思相同，都是在批评某个群体。句子2语法正确，表意清晰。"
}}
```

句子1: 你真是太笨了！
句子2: 我不同意你的观点。
```json
{{
    "toxic_pairwise_score": 1,
    "content_score": 0,
    "neutral_fluency": 1,
    "reason": "句子1是人身攻击，称对方'笨'，而句子2只是表达不同意见。两句话意思完全不同，一个是侮辱，一个是礼貌的不同意。句子2语法正确，表意清晰。"
}}
```

句子1: 这个政策很糟糕。
句子2: 这个政策存在一些问题。
```json
{{
    "toxic_pairwise_score": 1,
    "content_score": 0.5,
    "neutral_fluency": 1,
    "reason": "句子1使用'很糟糕'这种强烈的负面评价，比句子2的'存在一些问题'更具攻击性。两句话都是负面评价，但程度不同：'很糟糕'表示完全否定，'存在一些问题'表示部分问题，因此内容部分相同。句子2语法正确，表意清晰。"
}}
```

句子1: 这些人确实有问题。
句子2: 这些人确实有问题。
```json
{{
    "toxic_pairwise_score": 0.5,
    "content_score": 1,
    "neutral_fluency": 1,
    "reason": "两个句子完全相同，冒犯程度一致。意思完全相同。句子2语法正确，表意清晰。"
}}
```
</样本>