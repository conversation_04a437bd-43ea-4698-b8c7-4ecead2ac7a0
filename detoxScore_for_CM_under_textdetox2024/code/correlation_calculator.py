#!/usr/bin/env python3
"""
通用的相关性计算脚本
计算黄金标准文件和模型输出文件之间的皮尔森系数和斯皮尔曼系数
"""

import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr
import argparse
import glob
from pathlib import Path


def calculate_correlations(golden_file, model_files, column_name="j_score", output_format="markdown"):
    """
    计算相关性系数
    
    Args:
        golden_file: 黄金标准文件路径
        model_files: 模型输出文件列表
        column_name: 要比较的列名
        output_format: 输出格式 ("markdown", "csv", "json")
    
    Returns:
        list: 包含文件名、皮尔森系数、斯皮尔曼系数的列表
    """
    print(f"📊 开始计算相关性系数...")
    print(f"黄金标准文件: {golden_file}")
    print(f"比较列名: {column_name}")
    print(f"模型文件数量: {len(model_files)}")
    print("=" * 60)
    
    # 读取黄金标准文件
    try:
        if golden_file.endswith('.tsv'):
            df_golden = pd.read_csv(golden_file, sep='\t')
        else:
            df_golden = pd.read_csv(golden_file)
        
        if column_name not in df_golden.columns:
            print(f"❌ 黄金标准文件中未找到列 '{column_name}'")
            print(f"可用列: {list(df_golden.columns)}")
            return []
            
        col_golden = df_golden[column_name]
        print(f"✅ 黄金标准数据: {len(col_golden)} 条记录")
        
    except Exception as e:
        print(f"❌ 读取黄金标准文件失败: {e}")
        return []
    
    results = []
    
    for model_file in model_files:
        try:
            print(f"\n处理文件: {os.path.basename(model_file)}")
            
            # 读取模型输出文件
            df_model = pd.read_csv(model_file)
            
            if column_name not in df_model.columns:
                print(f"  ❌ 模型文件中未找到列 '{column_name}'")
                print(f"  可用列: {list(df_model.columns)}")
                continue
            
            col_model = df_model[column_name]
            
            # 检查数据长度是否匹配
            if len(col_golden) != len(col_model):
                print(f"  ⚠️  数据长度不匹配: 黄金标准={len(col_golden)}, 模型={len(col_model)}")
                # 取较短的长度
                min_len = min(len(col_golden), len(col_model))
                col_golden_subset = col_golden[:min_len]
                col_model_subset = col_model[:min_len]
            else:
                col_golden_subset = col_golden
                col_model_subset = col_model
            
            # 检查是否有缺失值
            if col_golden_subset.isna().any() or col_model_subset.isna().any():
                print(f"  ⚠️  发现缺失值，将被忽略")
                # 移除缺失值
                mask = ~(col_golden_subset.isna() | col_model_subset.isna())
                col_golden_subset = col_golden_subset[mask]
                col_model_subset = col_model_subset[mask]
            
            # 计算相关性系数
            pearson_corr, pearson_p = pearsonr(col_golden_subset, col_model_subset)
            spearman_corr, spearman_p = spearmanr(col_golden_subset, col_model_subset)
            
            results.append({
                'file': os.path.basename(model_file),
                'full_path': model_file,
                'pearson': pearson_corr,
                'pearson_p': pearson_p,
                'spearman': spearman_corr,
                'spearman_p': spearman_p,
                'sample_size': len(col_golden_subset)
            })
            
            print(f"  ✅ Pearson: {pearson_corr:.4f} (p={pearson_p:.4f})")
            print(f"     Spearman: {spearman_corr:.4f} (p={spearman_p:.4f})")
            print(f"     样本数: {len(col_golden_subset)}")
            
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            continue
    
    return results


def output_results(results, output_format="markdown", sort_by="pearson"):
    """
    输出结果
    
    Args:
        results: 计算结果列表
        output_format: 输出格式
        sort_by: 排序依据 ("pearson", "spearman")
    """
    if not results:
        print("❌ 没有有效的结果数据")
        return
    
    # 排序
    results.sort(key=lambda x: x[sort_by], reverse=True)
    
    print(f"\n📈 结果汇总 (按 {sort_by} 降序排列):")
    print("=" * 80)
    
    if output_format == "markdown":
        print("| 文件名 | Pearson | Spearman | 样本数 | Pearson p值 | Spearman p值 |")
        print("| --- | --- | --- | --- | --- | --- |")
        for result in results:
            print(f"| {result['file']} | {result['pearson']:.4f} | {result['spearman']:.4f} | {result['sample_size']} | {result['pearson_p']:.4f} | {result['spearman_p']:.4f} |")
    
    elif output_format == "csv":
        print("文件名,Pearson,Spearman,样本数,Pearson_p值,Spearman_p值")
        for result in results:
            print(f"{result['file']},{result['pearson']:.4f},{result['spearman']:.4f},{result['sample_size']},{result['pearson_p']:.4f},{result['spearman_p']:.4f}")
    
    elif output_format == "simple":
        for result in results:
            print(f"{result['file']:<40} Pearson: {result['pearson']:.4f}  Spearman: {result['spearman']:.4f}")


def find_model_files(directory, pattern="*.csv"):
    """
    查找模型输出文件
    """
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return []
    
    files = glob.glob(os.path.join(directory, pattern))
    files = [f for f in files if not os.path.basename(f).startswith('.')]  # 排除隐藏文件
    return sorted(files)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="计算相关性系数")
    parser.add_argument("--golden", "-g", required=True, help="黄金标准文件路径")
    parser.add_argument("--model-dir", "-d", help="模型输出文件目录")
    parser.add_argument("--model-files", "-f", nargs="+", help="模型输出文件列表")
    parser.add_argument("--column", "-c", default="j_score", help="比较的列名 (默认: j_score)")
    parser.add_argument("--format", choices=["markdown", "csv", "simple"], default="markdown", help="输出格式")
    parser.add_argument("--sort", choices=["pearson", "spearman"], default="pearson", help="排序依据")
    parser.add_argument("--pattern", default="*.csv", help="文件匹配模式 (默认: *.csv)")
    
    args = parser.parse_args()
    
    # 检查黄金标准文件
    if not os.path.exists(args.golden):
        print(f"❌ 黄金标准文件不存在: {args.golden}")
        return
    
    # 获取模型文件列表
    if args.model_files:
        model_files = args.model_files
        # 检查文件是否存在
        model_files = [f for f in model_files if os.path.exists(f)]
    elif args.model_dir:
        model_files = find_model_files(args.model_dir, args.pattern)
    else:
        print("❌ 请指定模型文件目录 (--model-dir) 或文件列表 (--model-files)")
        return
    
    if not model_files:
        print("❌ 未找到任何模型文件")
        return
    
    # 计算相关性
    results = calculate_correlations(args.golden, model_files, args.column, args.format)
    
    # 输出结果
    output_results(results, args.format, args.sort)


if __name__ == "__main__":
    main()
