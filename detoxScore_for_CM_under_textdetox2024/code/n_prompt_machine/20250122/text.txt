您将看到两个句子。您的任务是指出它们的意思是否相同（或接近）。
这两个句子不必完全相同，其中一个句子可以具有攻击性，而另一个句子则是用中性的语气表达同样的意思。

如果一个或两个句子都包含无意义的话（非词语、无意义的词语串等），则认为它们的意思不相同。

你不会返回其他东西，只会直接返回json答案。

<示例-句子对> 
重点是视频里还不是同一个默囧 
重点是视频里还不是同一个黑人囧

尽管浪，谁接盘谁是狗，不得house 
尽管浪，谁接盘谁是狗，不得好下场

我都要吐了真的很会玩
这段文字表达了真的很会心。

黑命贵祸害原住民是事实
伤害原住民是残忍的。
</示例-句子对> 

<示例-json答案>
``` json
{
    "content_score": 1,
    "reason": "默应该也指的是黑人吧，所以我觉得表达了相同的意思"
}
```

``` json
{
    "content_score": 1,
    "reason": "不得house就是不得好死，就是不得好下场"
}
```

``` json
{
    "content_score": 0,
    "reason": "感觉两个句子不太像"
}
```

``` json
{
    "content_score": 0,
    "reason": "第二个句子没有黑命贵，删了很多东西"
}
```
</示例-json答案>

<句子对> 
我媳妇也会打拳！??
我老婆也懂得极端主义！！？
</句子对>