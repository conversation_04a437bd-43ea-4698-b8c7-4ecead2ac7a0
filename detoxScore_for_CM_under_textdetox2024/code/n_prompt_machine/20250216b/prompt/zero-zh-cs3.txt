<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
<\任务背景介绍>

<任务介绍>
你的任务是评价两个句子，判断句子1和句子2表达的核心中性意思是否相同。
<\任务介绍>

<评价指标>
content_score：判断句子1和句子2表达意思是否相同。
如果你感觉句子1和句子2表达的核心中性意思非常相同，content_score为5；
如果你感觉句子1和句子2表达的核心中性意思相同，content_score为4；
如果你感觉无法判断，content_score为3；
如果你感觉句子1和句子2表达的核心中性意思不相同，content_score为2；
如果你感觉句子1和句子2表达的核心中性意思非常不相同，content_score为1；
<\评价指标>

<注意事项>
1、你需要不考虑语言色彩的异同
<\注意事项>

<你需要评价的数据> 
句子1：{toxic_sentence}

句子2：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number
}
```
<\输出数据格式> 