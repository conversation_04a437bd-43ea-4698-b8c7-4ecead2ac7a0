句子1是代表着互联网或者生活中带有黑话、污言秽语等文本

句子1：{toxic_sentence}

句子2：{neutral_sentence}

在不考虑语言色彩的情况下，句子1和句子2表达意思相同吗？

- 一句话中出现“某某打拳”这个说法，可能代表着“某某是极端女权主义者”。

非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。

输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number
}
```

<example-sentences>
句子1：广州有很多默。	
句子2：广州有很多黑人。
</example-sentences>

<example-return>
```json
{
    "content_score": 5
}
```
</example-return>
