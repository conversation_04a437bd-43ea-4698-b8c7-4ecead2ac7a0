<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
<\任务背景介绍>

<任务介绍>
你的任务是按照要求判断句子流畅性。
<\任务介绍>

<评价指标>
neutral_fluency 可选值为 0、0.5、1，
0 = 完全无法理解，存在严重语法/逻辑断裂，比如【？？？啊？？我？？】
0.5 = 存在明显语病但勉强可懂，比如句子里“借用”与“优点”的搭配不当，导致语病
1 = 符合语法规范且表意清晰
<\评价指标>

<你需要评价的句子>
{neutral_sentence}
</你需要评价的句子>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
"neutral_fluency": number
}
```
<\输出数据格式> 