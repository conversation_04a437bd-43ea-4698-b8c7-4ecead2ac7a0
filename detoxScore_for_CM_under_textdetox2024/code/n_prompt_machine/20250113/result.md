# 测试例子
## 1. 例子 假阴性

句子1：还有人说默是人？

句子2：还有人把黑人当作人看待吗？

大模型：0 句子1表达的是对某人的称呼或身份的疑问，而句子2表达的是对黑人的态度和看待方式的问题，两者语义不同。

人工：1

### 针对性设计 prompt1
句子1：{toxic_sentence}

句子2：{neutral_sentence}

在不考虑语言色彩的情况下，句子1和句子2表达意思相同吗？

特例：互联网黑话中，一句话中突然出现一个“默”字，一般指的是“黑人”的意思。

非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。

输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```

## prompt2
将20250110的prompt1的结果和20250113的结果进行对比，只有同时评分低于3时，才认为为0，否则为1。


## prompt 3
## 2. 例子 假阴性
	
句子1：黑河南、黑北方的都是南满

句子2：批评河南、批评北方的人都是南方人

大模型：0 句子1表达的是对某人的称呼或身份的疑问，而句子2表达的是对黑人的态度和看待方式的问题，两者语义不同。

人工：1

# 结果
之前最好：20250110的prompt1 0.1236
目标超过：0.14
| Prompt | Pearson | Spearman |
| --- | --- | --- |
| ./results/prompt1.csv | 0.1152 | 0.1152 |
| ./results/prompt2.csv | 0.1322 | 0.1322 |
| ./results/prompt3.csv | 0.0782 | 0.0458 |