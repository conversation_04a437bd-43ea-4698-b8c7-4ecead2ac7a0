<任务说明>
请你评价提供给你的两个句子。
你的任务是判断它们相比较起来，哪个更加充满污言秽语让人不适。
如果句子1比句子2更加充满污言秽语让人不适，那么你的评价标注是1。
但是，如果句子2比句子1更加充满污言秽语让人不适，那么你的评价标注是0。
但是，如果句子1和句子2给你带来的感觉一样的(比如都感觉强烈不适，或者感觉都没啥的)，那么你的评价标注是0.5。
</任务说明>

<返回格式要求>
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "toxic_pairwise_score": number
}
```
</返回格式要求>

<两个句子>
句子1: {toxic_sentence}
句子2: {neutral_sentence}
</两个句子>

<example-sentences>
sentences 1: are they liars or just clueless ?
sentences 2: are they dishonest or simply unaware?
```json
{
    "toxic_pairwise_score": 1
}
```

sentences 1: liar , liar pants on fire justin .	
sentences 2: liar, liar, your story is falling apart, Justin.
```json
{
    "toxic_pairwise_score": 0
}
```


sentences 1: still standing up for your lying crazy leader who loves himself and nobody else .
sentences 2: still defending your dishonest and self-absorbed leader who seems to care only for himself.
```json
{
    "toxic_pairwise_score": 0
}
```
</example-sentences>