<instruction>
You will be shown two sentences. Your task is to select which of the texts is more offensive. 

The offensiveness of the sentences could be identical (for example, both sentences are identically neutral or identically offensive).
</instruction>

<object>
sentences 1: {toxic_sentence}
sentences 2: {neutral_sentence}
</object>

<score-role>
Sentence 1 is much more offensive than sentence 2, score it as 5 points; Sentence 1 is more offensive than sentence 2, score it as 4 points; The offensiveness of the sentences are identical; Sentence 2 is more offensive than sentence 1, score it as 2 points; Sentence 2 is much more offensive than sentence 1, score it as 1 point.
</score-role>

<return-style>
The return should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

```json
{
    "toxic_pairwise_score": number
}
```
</return-style>

<example-sentences>
sentences 1: are they liars or just clueless ?
sentences 2: are they dishonest or simply unaware?
```json
{
    "toxic_pairwise_score": 5
}
```

sentences 1: liar , liar pants on fire justin .	
sentences 2: liar, liar, your story is falling apart, <PERSON>.
```json
{
    "toxic_pairwise_score": 2
}
```


sentences 1: still standing up for your lying crazy leader who loves himself and nobody else .
sentences 2: still defending your dishonest and self-absorbed leader who seems to care only for himself.
```json
{
    "toxic_pairwise_score": 2
}
```
</example-sentences>