<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
现在的模型
<\任务背景介绍>

<任务介绍>
您将看到两个句子。您的任务是判断句子1比句子2具有更大的冒犯性吗？
<\任务介绍>

<评价指标>
toxic_pairwise_score：句子1比句子2具有更大的冒犯性吗？
5:非常具有；
4:具有；
3:句子1和句子2的冒犯性相同；
2:不具有；
1:非常不具有；
<\评价指标>

<你需要评价的数据> 
句子1: {toxic_sentence}
句子2: {neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "toxic_pairwise_score": number
}
```
<\输出数据格式>

<样本>
sentences 1: no ones asking you to invade shit .
sentences 2: noone is asking you to interfere.

```json
{
    "toxic_pairwise_score": 3
}
```

sentences 1: liar , liar pants on fire elon .
sentences 2: liar, liar, your story is falling apart, Elon.
```json
{
    "toxic_pairwise_score": 1
}
```

sentences 1: is he liars or just clueless ?
sentences 2: is he dishonest or simply unaware?
```json
{
    "toxic_pairwise_score": 5
}
```

sentences 1: How the hell are they going to block me from editting?
sentences 2: How are they planning to prevent me from editing?
```json
{
    "toxic_pairwise_score": 5
}
```
</样本>