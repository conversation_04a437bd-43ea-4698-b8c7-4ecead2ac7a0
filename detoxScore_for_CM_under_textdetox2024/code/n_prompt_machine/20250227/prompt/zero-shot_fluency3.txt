<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
<\任务背景介绍>

<任务介绍>
你将看到一个句子。你的任务是判断句子的流畅性。请注意，句子是否粗鄙和流畅性无关。
<\任务介绍>

<评价指标>
neutral_fluency：代表句子的流畅性，值为1到5的整数。
如果你感觉句子读起来自然、连贯，没有任何不自然或难以理解的地方，那么句子属于非常流畅，设置neutral_fluency为5；
如果你感觉句子整体比较通顺，但可能有少量小瑕疵，那么句子属于比较流畅，设置neutral_fluency为4；
如果你感觉句子勉强可以理解，但存在一些不自然或不够通顺的地方，那么句子属于一般流畅，设置neutral_fluency为3；
如果你感觉句子读起来有明显的不连贯或不自然之处，理解起来有些困难，那么句子属于不太流畅，设置neutral_fluency为2；
如果你感觉句子很难理解，存在严重的语法错误或逻辑问题，那么句子属于非常不流畅，设置neutral_fluency为1。
<\评价指标>

<你需要评价的数据> 
句子：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "neutral_fluency": number
}
```
<\输出数据格式> 