<你需要评价的数据>
{neutral_sentence}
</你需要评价的数据>

<分析流程>
1. ​结构完整性检查
- 检测逻辑断裂/语义跳跃
- 识别不连贯的段落衔接
2. ​错误等级判定​（优先级顺序）
- A类错误：乱码/不可解析符号（*^>等）、语义完全断裂、主谓搭配失当、动宾搭配断裂、反逻辑语义组合
- B类错误：拼写/粘连错误（如"戏精-讨好"）、不影响理解的语法错误
- C类错误：标点位置/大小写/格式不规范
3. ​流畅度评分
1分：无错误或仅存在C类错误
0.5分：存在B类错误但保持可读性
0分：存在A类错误导致理解困难
</分析流程>

<输出数据格式>
请按以下模式生成包含评估结果的JSON对象：
{
    "neutral_fluency": number // 流畅度评分(0-1)
}
</输出数据格式>

<执行要求>
1. 严格遵循错误等级优先级（A类>B类>C类）
2. 对特殊符号保持敏感但不过度反应
3. 中文互联网文本特性适配
4. 评分需与错误类型严格对应
5. 原因说明需包含具体错误示例
</执行要求>