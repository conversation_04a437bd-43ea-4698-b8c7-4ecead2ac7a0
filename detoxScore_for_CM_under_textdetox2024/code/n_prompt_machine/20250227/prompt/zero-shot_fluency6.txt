<任务背景介绍> 
有毒文本指包含攻击性、歧视性等有害内容的文本。
自然语言处理领域的文本解毒任务旨在对有毒文本进行处理，去除或修正其中有害内容，得到中性文本。
文本解毒任务的评价标准主要有三种：风格迁移准确性，即中性文本是否没有原有毒文本这么冒犯；内容保留度，即中性文本是否和原有毒文本表达的核心意思相同；流畅性，即中性文本是否流畅，是否能让人理解表达意思。
你将参与一个文本解毒任务的评价工作，按照要求对于具体的指标进行评价。
<\任务背景介绍>

<任务介绍>
你将看到一个句子。你的任务是判断句子的流畅性。
<\任务介绍>

<评价指标>
neutral_fluency：代表句子的流畅性，值为1到5的整数。
如果你感觉句子读起来自然、连贯，没有任何不自然或难以理解的地方，那么设置为5；
如果你感觉句子整体比较通顺，但可能有少量小瑕疵，那么设置为4；
如果你感觉句子勉强可以理解，但存在一些不自然或不够通顺的地方，那么设置为3；
如果你感觉句子读起来有明显的不连贯或不自然之处，理解起来有些困难，那么设置为2；
如果你感觉句子很难理解，存在严重的语法错误或逻辑问题，那么句子属于非常不流畅，那么设置为1。
<\评价指标>

<你需要评价的数据> 
句子：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "neutral_fluency": number,
    "reason": string
}
```
<\输出数据格式> 