<任务背景介绍> 
有毒文本指包含攻击性、歧视性等有害内容的文本。
自然语言处理领域的文本解毒任务旨在对有毒文本进行处理，去除或修正其中有害内容，得到中性文本。
文本解毒任务的评价标准主要有三种：风格迁移准确性，即中性文本是否没有原有毒文本这么冒犯；内容保留度，即中性文本是否和原有毒文本表达的核心意思相同；流畅性，即中性文本是否流畅，是否能让人理解表达意思。
你将参与一个文本解毒任务的评价工作，按照要求对于具体的指标进行评价。
<\任务背景介绍>

<任务介绍>
您将看到两个句子。您的任务是判断哪一个句子更具冒犯性。这两个句子的冒犯性程度可能相同（例如，两个句子都是同样中性的，或者同样冒犯性）。
<\任务介绍>

<评价指标>
toxic_pairwise_score：哪一个句子更具有冒犯性，可选值为[5,4,3,2,1]。
要是你觉得句子1比句子2冒犯得多很多，那就给toxic_pairwise score赋值为5。
要是你觉得句子1比句子2冒犯得多，那就给toxic_pairwise score赋值为4。
要是你觉得句子1和句子2冒犯得一样，那就给toxic_pairwise score赋值为3。
要是你觉得句子1和句子2冒犯得少，那就给toxic_pairwise score赋值为2。
要是你觉得句子1和句子2冒犯得少很多，那就给toxic_pairwise score赋值为1。
<\评价指标>

<你需要评价的数据> 
句子1: {toxic_sentence}
句子2: {neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“```”：

```json
{
    "toxic_pairwise_score": number
}
```
<\输出数据格式>

<样本>
句子1: 我的母亲也会参与一些激进的性别平等讨论！??
句子2: 我妈妈也会打拳！??
```json
{
    "toxic_pairwise_score": 2
}
```

句子1: 国楠就爱看哪吒
句子2: 国内男性就爱看哪吒
```json
{
    "toxic_pairwise_score": 5
}
```

句子1: 还有美国人说默是人？
句子2: 还有美国人把黑人当作人看待吗？
```json
{
    "toxic_pairwise_score": 4
}
```
</样本>