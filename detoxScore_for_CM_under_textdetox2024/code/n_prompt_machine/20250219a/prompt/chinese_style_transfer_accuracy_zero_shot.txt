<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
<\任务背景介绍>

<任务介绍>
您将看到两个句子。您的任务是判断哪一个句子更具冒犯性。这两个句子的冒犯性程度可能相同（例如，两个句子都是同样中性的，或者同样冒犯性）。
<\任务介绍>

<评价指标>
toxic_pairwise_score：哪一个句子更具有冒犯性，可选值为[5,4,3,2,1]。
如果你感觉句子1比句子2更加冒犯得多时，toxic_pairwise_score为5；
如果你感觉句子1比句子2更加冒犯时，toxic_pairwise_score为4；
如果你感觉句子1和句子2的冒犯程度相同时，toxic_pairwise_score为3；
如果你感觉句子2比句子1更加冒犯时，toxic_pairwise_score为2；
如果你感觉句子2比句子1更加冒犯得多时，toxic_pairwise_score为1；
<\评价指标>

<你需要评价的数据> 
句子1: {toxic_sentence}
句子2: {neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "toxic_pairwise_score": number
}
```
<\输出数据格式>

我媳妇也会打拳！??	我的妻子也会参与一些激进的性别平等讨论！??

<样本>
句子1: 我的母亲也会参与一些激进的性别平等讨论！??
句子2: 我妈妈也会打拳！??
```json
{
    "toxic_pairwise_score": 2
}
```
</样本>