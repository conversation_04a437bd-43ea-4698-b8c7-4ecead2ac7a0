<instruction>
You will be shown two sentences. Your task is to select which of the texts is more offensive. 

The offensiveness of the sentences could be identical (for example, both sentences are identically neutral or identically offensive).
</instruction>

<object>
sentences 1: {toxic_sentence}
sentences 2: {neutral_sentence}
</object>

<score-role>
Sentence 1 is much more offensive than sentence 2, score it as 5 points; Sentence 1 is more offensive than sentence 2, score it as 4 points; The offensiveness of the sentences are identical; Sentence 2 is more offensive than sentence 1, score it as 2 points; Sentence 2 is much more offensive than sentence 1, score it as 1 point.
</score-role>

<return-style>
The return should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

```json
{
    "reason": string,
    "toxic_pairwise_score": number
}
```
</return-style>

<example-sentences>
sentences 1: Is he a thief or merely forgetful?
sentences 2: Is he a stealer or only absent-minded?
```json
{
    "toxic_pairwise_score": 5
}
```

sentences 1: Cheat, cheat, your deception is transparent, Mark. 
sentences 2: Cheat, cheat, the truth is shining through, <PERSON>.
```json
{
    "toxic_pairwise_score": 2
}
```


sentences 1: Always sticking up for that deceptive egomaniac who thinks the world revolves around him. 
sentences 2: Continuously supporting that deceitful narcissist who believes everything revolves around him.
```json
{
    "toxic_pairwise_score": 2
}
```
</example-sentences>