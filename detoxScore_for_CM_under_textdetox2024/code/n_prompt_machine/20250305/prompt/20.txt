<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
现在文本解毒任务模型针对有毒句子进行了去毒化改写，得到了无毒版本的句子，现在我要判断无毒版本的句子是否还保留了原来的核心意思。请你帮我判断。
说明：
- 不考虑语气和情感上的差异；
- 比考虑句子是否一个讽刺和侮辱一个没有讽刺和侮辱的差异；
- 不考虑句子是否一个有歧视意味一个没有歧视意味的差异；
- 不考虑句子是否一个带有侮辱性词语一个没有带有侮辱性词语的差异；
- - 如“他长得是个屎”和“他的外表并不吸引人”意思相同；
- - 如“下贱的杂种”和“行为卑鄙的人”意思相同。
- 不考虑句子是否有特定的负面和攻击性含义的差异；
- 句子1是代表着互联网或者生活中带有黑话、污言秽语等文本。

以下意思可能相同的点：
- “某某打拳”这个说法，也有代表着“某某参与激进的性别平等讨论”；
- “幕刃”这个说法，也有代表着“极端女权主义者”；
- “小仙女”这个说法，也有代表着“自我感觉良好的女性”；
- “默”、“嘿嘿”、“黑蛆”在互联网上有时候是“黑人”的黑称，背后是同一个意思；
- - 比如“就是嘿嘿吧？”和“就是指黑人吗？”，背后是同一个意思。
- “母狗”在互联网上有时候是“不受欢迎的女性”的黑称，背后是同一个意思。

以下意思可能不相同或者非常不相同的点：
- 添加额外主语，比如“又不喜欢小黑”- “我不喜欢黑人”。
<\任务背景介绍>

<任务介绍>
你的任务是判断句子1和句子2表达的中性意思是否相同。
<\任务介绍>

<评价指标>
content_score：非常可能相同为5分，可能相同为4分，无法判断为3分，可能不相同为2分，非常可能不相同为1分。
<\评价指标>

<你需要评价的数据> 
句子1是代表着互联网或者生活中带有黑话、污言秽语等文本

句子1：{toxic_sentence}

句子2：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": string
}
```
<\输出数据格式> 
