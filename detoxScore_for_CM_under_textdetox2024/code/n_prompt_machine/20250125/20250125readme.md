# costar-4 prompt
这个是直接改写文章的最后的内容，效果不好，看起来是因为我的prompt语句没有说明白。
比如
 {\n        "reason": "有毒文本中的\'母狗\'和中性文本中的\'他人的批评\'指代相似的情况，但表达方式不同，意思相近。",\n        "content_score": 2\n    }
 这里都说了意思相近了，结果content_score还是2！应当为4才对。

 考虑在原来的基础上改

| 20250125Prompt | Pearson | Spearman | 统计 |
| --- | --- | --- | --- |
| 人类标注数据 | 1 | 1 | 1:91，0:9| 
| zero-shot | 0.2440 | 0.2440 | 1: 84，0:16| 
| few-shot： 使用了co-star。prompt还需要优化，已经是正的额。| 0.0361 | 0.0361 |1: 68，0:32| 
| few-shot：<zero-shot prompt, example>。* 考虑继续在此基础上优化下example | 0.0839 | 0.0839 |1:84， 0:16| 
| few-shot：<example, zero-shot prompt>。出现过拟合现象，不继续在此基础上优化 | -0.0989 | -0.0989 |1:91，0:9| 