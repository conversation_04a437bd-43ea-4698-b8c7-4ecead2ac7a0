
<example-sentences>
句子1：广州有很多默。	
句子2：广州有很多黑人。

句子1：你天天在网上打拳，还用上班吗！??	
句子2：你天天在网上进行激烈的女权讨论，还用上班吗！??

句子1：每天都会在热搜上看到明星的舔狗
句子2：每天都会在热搜上看到过分讨好明星的人
</example-sentences>

<example-return>
```json
{
    "content_score": 5,
    "reason": "在文本解毒任务的背景下，句子1中的'默'是互联网黑话中的”黑人“的蔑称。所以虽然句子2用了更文明的说法。但是句子1和句子2表达的是同一个意思。所以句子1和句子2表达意思非常相同。"
}
```

```json
{
    "content_score": 4,
    "reason": "在文本解毒任务的背景下，句子1中的'打拳'是互联网黑话代指进行激进的男女平权运动、女权运动等，所以句子1和句子2表达的意思相同。"
}
```

```json
{
    "content_score": 5,
    "reason": "舔狗的中性词汇就是过分讨好某人的人，所以在不考虑语言色彩的情况下，句子1和句子2很相似"
}
```
</example-return>
句子1是代表着互联网或者生活中带有黑话、污言秽语等文本

句子1：{toxic_sentence}

句子2：{neutral_sentence}

在不考虑语言色彩的情况下，句子1和句子2表达意思相同吗？

- 一句话中出现“某某打拳”这个说法，可能代表着“某某是极端女权主义者”。

非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。

输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```
