{"cells": [{"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from zhipuai import ZhipuAI\n", "import re\n", "import json\n", "import pandas as pd\n", "import os\n", "\n", "df_c = pd.read_csv(\n", "    \"../../data/CM/ZH.tsv\",\n", "    sep=\"\\t\",\n", ")\n", "\n", "with open(\"../../config.json\", \"r\") as f:\n", "    config = json.load(f)\n", "\n", "glmConfig = config.get(\"glm\")\n", "\n", "\n", "client = ZhipuAI(api_key=glmConfig[\"apiKey\"])  # 填写您自己的APIKey\n", "\n", "\n", "def Detox_zero_shot():\n", "    response = client.chat.completions.create(\n", "        temperature=glmConfig[\"temperature\"],\n", "        model=glmConfig[\"model\"],  # 填写需要调用的模型编码\n", "        messages=[\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": \"我想要你扮演一个众包平台的数据标注人员来评价提供的数据集。请你按照任务要求进行评价。对于每次评价，你需要给出确切而明确的答案。\",\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": '''# 背景 # \n", "我组织了一场自然语言处理领域的文本解毒比赛。比赛任务是给定一段具有攻击性或不恰当内容的有毒文本，参赛者的模型能够将其重新改写为无攻击性或不恰当内容的无毒文本，同时尽可能保留主要内容。\n", "有毒文本是指那些明显包含粗俗、冒犯性词汇的文本，但这些文本仍然包含有意义的中性内容。换句话说，这些文本的毒性是通过使用不适当的词汇或表达方式来体现的，而不是通过其核心含义。在任务中，对于有毒文本的判断主要基于显式的粗俗和冒犯性词汇，而不是基于语义的隐含意图。这种定义方式是为了确保任务的可操作性，因为隐含毒性类型的解毒难度更大，且很难通过自动化的模型来准确评估。\n", "现在我掌握了这个比赛的数据集，里面包括了【有毒文本，中性文本】\n", "\n", "#############\n", "\n", "# 目标 #\n", "我想要你评价这个数据集里每个数据中的有毒文本和中性文本是否表达同一个意思，以帮助我评价各个参赛者的模型的性能。使用下面指示一步一步完成，并且不要使用任何代码。\n", "分析：\n", "1. TOXIC_SENTENCE_MEANING：分析有毒文本的意思，。\n", "2. NEUTRAL_SENTENCE_MEANING: 分析中性文本表达的意思。\n", "3. EVAL_REASON: 对比[TOXIC_SENTENCE_MEANING]，[NEUTRAL_SENTENCE_MEANING]，评价有毒文本和中性文本是否表达同一个意思。\n", "评价：\n", "4. CONTENT_SCORE: 根据[EVAL_REASON]，有毒文本和中性文本表达的意思非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。\n", "\n", "#############\n", "\n", "# 形式 #\n", "自然语言处理任务的模型的评价JSON\n", "\n", "#############\n", "\n", "# 语调 #\n", "专业的, 技术的\n", "\n", "#############\n", "\n", "# 受众 #\n", "\n", "参与文本解毒比赛的评委、参赛队伍，以及对其感兴趣的老师和学生。说服他们你的评价经过深思熟虑，并有充分的理由。\n", "\n", "#############\n", "\n", "# 你的返回: JSON 数组 #\n", "对于每个有毒文本，中性文本对，\n", "- reason: [EVAL_REASON]\n", "- content_score: [CONTENT_SCORE]\n", "\n", "#############\n", "\n", "# 开始评价 # \n", "\n", "如果你明白了，就向我索取我的数据集。''',\n", "            },\n", "            {\n", "                \"role\": \"assistant\",\n", "                \"content\": \"明白了，请提供您的数据集中的几个有毒文本和中性文本对，我将根据您提供的指示进行评价。\",\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": '''tsv```toxic_sentence\tneutral_sentence\n", "没事，不是说舔狗很多吗？幕刃召唤舔狗就算了\t没关系，不是说有很多单方面付出的人吗？极端女权主义者吸引这些人也就算了\n", "有些幕刃真的是。。。。\t有些极端女权主义者真的是。。。。\n", "nm艾特老子干什么\t为什么在微博上提到我\n", "男同没皮没脸要借别人的好脸\t有些男同性恋者不顾他人感受，借用他人的优点\n", "政治正确这个词用的好，现在接受同性恋居然变成了主流，对同性恋稍微表示恶心居然就成为了反面派\t政治正确这个词使用得很到位，现在接受同性恋已经成为主流趋势，即使是对同性恋有一点不适感也会被视为持反对意见的人\n", "被默光顾过就让默带走啊，留下来祸害同胞？\t如果被黑人影响过，就让黑人带走，不要留下来影响其他人\n", "舔狗也本来不是结婚去的，骗炮的，骗钱的，女拳自己都知道不靠谱\t单方面付出的人本来并不是为了结婚，有的是欺骗感情，有的是骗取财物，极端女权主义者自己也知道这是不可靠的\n", "如果你把基佬打伤，他的伤口沾到你的血或体液，他就会感染hiv。\t如果你伤害了一个男同性恋者，并且他的伤口接触到了你的血液或体液，他可能会感染HIV\n", "屠黑屠绿屠同性，爱国爱党爱人民\t屠黑屠绿屠同性，爱国爱党爱人民\n", "出来引战就别怕被骂啊母狗\t如果你故意挑起争端，就不应害怕他人的批评\n", "我觉得我也思想有点问题，是个女生双性恋，但是我觉得女女可接受（因为我也是）但是我觉得男男有点ex???我一度怀疑自己是不是毛病...明明自己也是被这个世界不认可的人居然会有这种思想，真的很不好意思\t我感觉自己的观念可能有些问题。作为一个女性双性恋者，我能够接受女女性关系（因为我自己也是）。但我个人对男男性关系有些不适。我曾怀疑自己是否有问题...毕竟，作为一个在这个世界上不被认可的人，我居然会有这样的想法，这让我感到非常尴尬\n", "实话实说，这小黑孩的照片我居然感觉有些惊悚\t说实话，我对这个黑人小孩的照片感到有些不安\n", "因为默的长相不符合我国大部分人的审美\t因为黑人的外表不符合我们国家大多数人的审美标准\n", "哪来的通讯录怪物\t哪里来的令人反感的同性恋者\n", "更何况你鬼母阿娜本身就是个二年级辍学的屑\t更何况你本人，阿娜，只是一个二年级就辍学的人\n", "男同喜欢封面上的那种?\t男同性恋者喜欢封面上展示的那种类型吗？\n", "喂喂喂？基佬还能讲话吗\t喂喂喂？同性恋者还能发言吗\n", "纯黑都能在中国找到女人，混黑还会找不到？黑皮只会越来越多！！\t纯种黑人都能在中国找到伴侣，混血黑人更不会找不到。未来黑人混血儿的数量只会不断增加\n", "这张动态图简直把虫类及其它们后代的智商表现淋漓尽致\t这张动态图充分展示了某些人及其后代的智力水平\n", "因为丈夫（未婚夫），公婆是真正受压迫者。那些中老年断子老苟内心巴不得这种状况愈演愈烈，它们好作威作福，充分体会欺负人的乐趣，所以它们是网络女拳的主力军\t在某些情况下，丈夫（未婚夫）和公婆才是真正的受害者。一些中老年的无子女的男性可能内心希望这种情况越来越严重，这样他们就可以逞威风，享受压迫他人的乐趣，因此他们是网络上极端女权主义的主要支持者\n", "好家伙，整活老哥把女拳的活性转然后买抖+推给女拳粉丝炸鱼\t真是厉害，这位活跃用户将极端女权主义者的动态转化为购买力，然后通过抖音平台推广给极端女权主义者的粉丝\n", "我们的妇女同志对外民族的包容程度已经达到人类进化史的巅峰，想必用不多久，全世界人民真的可以团结在一起呢\t我们的妇女对外来民族的包容性已经达到了一个非常高的水平，可以预见在不久的将来，全世界的人民真的能够实现团结```''',\n", "            }\n", "        ],\n", "    )\n", "    return response\n", "\n", "r = Detox_zero_shot()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["'以下是对您提供的数据集中的每个有毒文本和中性文本对的评价，按照您提供的指示进行：\\n\\n```json\\n[\\n    {\\n        \"reason\": \"有毒文本中的\\'舔狗\\'和中性文本中的\\'单方面付出的人\\'虽然表达方式不同，但核心意思相近，都指代一种不平等的关系。\\'幕刃\\'和\\'极端女权主义者\\'也具有相似的指代意义。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本中的\\'幕刃\\'和中性文本中的\\'极端女权主义者\\'指代同一群体，尽管表达方式不同，但意思相同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本中的\\'nm艾特老子\\'和中性文本中的\\'为什么在微博上提到我\\'表达的意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本中的\\'男同没皮没脸\\'和中性文本中的\\'有些男同性恋者不顾他人感受\\'表达的意思相近，但有毒文本带有明显的贬义。\",\\n        \"content_score\": 3\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都讨论了\\'政治正确\\'和\\'同性恋\\'的话题，表达的意思基本相同，只是语言风格和情感色彩不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本中的\\'默光顾\\'和中性文本中的\\'黑人影响\\'指代相似的情况，但表达方式不同，意思相近。\",\\n        \"content_score\": 3\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都描述了\\'单方面付出的人\\'的行为动机，表达的意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都描述了伤害男同性恋者可能导致HIV感染的情况，表达的意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本完全相同，表达的意思完全一致。\",\\n        \"content_score\": 5\\n    },\\n    {\\n        \"reason\": \"有毒文本中的\\'母狗\\'和中性文本中的\\'他人的批评\\'指代相似的情况，但表达方式不同，意思相近。\",\\n        \"content_score\": 2\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都表达了对自己观念的怀疑和尴尬，意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都表达了对黑人小孩照片的不安感，意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都描述了黑人的外表不符合审美标准，意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本中的\\'通讯录怪物\\'和中性文本中的\\'令人反感的同性恋者\\'指代同一群体，但表达方式不同，意思相近。\",\\n        \"content_score\": 2\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都描述了阿娜的学历背景，意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都询问了男同性恋者的喜好，意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本中的\\'基佬\\'和中性文本中的\\'同性恋者\\'指代同一群体，但表达方式不同，意思相近。\",\\n        \"content_score\": 3\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都讨论了黑人在中国的伴侣问题，意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都描述了动态图展示的智力水平，意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都讨论了中老年无子女男性在极端女权主义中的角色，意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都描述了将极端女权主义者的动态转化为购买力的行为，意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    },\\n    {\\n        \"reason\": \"有毒文本和中性文本都表达了对妇女包容性的看法，意思相同，只是语言风格不同。\",\\n        \"content_score\": 4\\n    }\\n]\\n```\\n\\n这些评价基于对有毒文本和中性文本的语义分析，确保了评价的准确性和一致性。希望这些评价能帮助您更好地评估参赛者的模型性能。'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["r.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["r2 = "]}], "metadata": {"kernelspec": {"display_name": "fight4human", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}