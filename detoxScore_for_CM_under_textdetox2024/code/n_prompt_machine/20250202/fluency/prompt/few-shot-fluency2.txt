<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
<\任务背景介绍>

<任务介绍>
你的任务是分别判断所提供的句子是否流畅，同时你要给出简洁的理由，说明为什么这么判断。
<\任务介绍>

<评价指标>
neutral_fluency：句子的流畅度，可选值为[1, 0.5, 0]，其中：
1 表示 “完全正确或存在小错误”：如果句子书写完全正确，单词使用形式正确且恰当。同时，我们不处理标点符号错误——我们允许逗号、句号在句子中出现位置错误的情况。我们还允许词语大小写错误——句子可能以小写字母开头，专有名词也可能小写，或者某个单词突然以大写字母开头。如果仅有标点错误或大小写错误，该句子的neutral_fluency为1；
0.5 表示"存在错误但不影响理解"：若文本中存在拼写错误——词语形态不正确、连写错误、拼写错误等，但整体意思仍可完全理解，则该句子的neutral_fluency为0.5；
0 表示"存在阻碍理解的错误"：若词语形态严重变形、使用完全不当、出现怪异符号，导致全部或部分文本内容无法理解，则该句子的neutral_fluency为0；
<\评价指标>

<你需要评价的数据> 
句子：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "neutral_fluency": number
}
```
<\输出数据格式> 

<样本>
句子：Who are these people? It rained and everyone ran away, all in vain...
neutral_fluency: 1

句子：hi Julia, how are you
neutral_fluency: 1

句子：So what, it worked out anyway
neutral_fluency: 1

句子：What's good abot it? They're just some weirdoo people.
neutral_fluency:  0.5

句子：I've made a brick like ths myself
neutral_fluency:  0.5

句子：always hating people like that. . . .
neutral_fluency:  0.5

句子：which [UNK] . . [UNK] . . . .
neutral_fluency:  0

句子：Know your place pinakleypinakleypinakleypinakleypinakley
neutral_fluency:  0

句子：whhhht your if lksjf
neutral_fluency:  0
</样本>