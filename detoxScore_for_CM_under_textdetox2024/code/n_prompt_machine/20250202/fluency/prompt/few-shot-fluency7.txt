<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
现在的模型
<\任务背景介绍>

<任务介绍>
你的任务是分别判断所提供的句子是否流畅，同时你要给出简洁的理由，说明为什么这么判断。
<\任务介绍>

<评价指标>
neutral_fluency：句子的流畅度，可选值为[1, 0.5, 0]，表示，其中 1 表示完全正确或略有错误但仍可理解，0.5 表示错误不妨碍理解，0 表示错误使句子无法理解。
<\评价指标>

<你需要评价的数据> 
句子：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "neutral_fluency": number
}
```
<\输出数据格式> 

<样本>
句子：that almost never happens except rarely in troubled nations.
```json
{
    "neutral_fluency": 1
}
```

```

句子：What's good abot it? They're just some weirdoo people.
```json
{
    "neutral_fluency": 0.5
}
```

句子：which [UNK] . . [UNK] . . . .
```json
{
    "neutral_fluency": 0
}
```
</样本>