<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
现在的模型
<\任务背景介绍>

<任务介绍>
您将看到两个句子。您的任务是选择哪一个句子更具冒犯性。这两个句子的冒犯性程度可能相同（例如，两个句子都是同样中性的，或者同样冒犯性）。
<\任务介绍>

<评价指标>
toxic_pairwise_score：哪一个句子更具有冒犯性，可选值为[5,4,3,2,1]。其中 5表示句子1比句子2具有大得多的攻击性；4表示句子1比句子2具有更多的攻击性；3表示句子1与句子2的攻击性相同；2表示句子2比句子1具有更多的攻击性；1表示句子2比句子1具有大得多的攻击性。

<\评价指标>

<你需要评价的数据> 
句子1: {toxic_sentence}
句子2: {neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "toxic_pairwise_score": number
}
```
<\输出数据格式>

<样本>
句子1: Is he a thief or merely forgetful?
句子2: Is he a stealer or only absent-minded?
```json
{
    "toxic_pairwise_score": 5
}
```

句子1: Cheat, cheat, your deception is transparent, Mark. 
句子2: Cheat, cheat, the truth is shining through, Mark.
```json
{
    "toxic_pairwise_score": 2
}
```


句子1: Always sticking up for that deceptive egomaniac who thinks the world revolves around him. 
句子2: Continuously supporting that deceitful narcissist who believes everything revolves around him.
```json
{
    "toxic_pairwise_score": 2
}
```
</样本>