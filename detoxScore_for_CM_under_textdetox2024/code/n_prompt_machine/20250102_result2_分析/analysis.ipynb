{"cells": [{"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["| Model | Accuracy | Recall | Precision | F1 |\n", "| --- | --- | --- | --- | --- |\n", "| ./results2/prompt1.csv | 0.6200 | 0.3743 | 0.1619 | 0.1892 |\n", "| ./results2/prompt10.csv | 0.6900 | 0.2464 | 0.1760 | 0.2054 |\n", "| ./results2/prompt2.csv | 0.6700 | 0.3886 | 0.1786 | 0.2171 |\n", "| ./results2/prompt3.csv | 0.6400 | 0.1829 | 0.1407 | 0.1590 |\n", "| ./results2/prompt4.csv | 0.6500 | 0.1857 | 0.1413 | 0.1605 |\n", "| ./results2/prompt5.csv | 0.6500 | 0.1857 | 0.1413 | 0.1605 |\n", "| ./results2/prompt6.csv | 0.6500 | 0.1857 | 0.1429 | 0.1615 |\n", "| ./results2/prompt7.csv | 0.6900 | 0.1971 | 0.1408 | 0.1643 |\n", "| ./results2/prompt8.csv | 0.6900 | 0.4929 | 0.2586 | 0.3286 |\n", "| ./results2/prompt9.csv | 0.6900 | 0.1971 | 0.1394 | 0.1633 |\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Recall is ill-defined and being set to 0.0 in labels with no true samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}], "source": ["# 读取文件A和文件B，其中A文件为gloden标注文件，B文件为模型预测文件\n", "# 输出文件A列名为x的列和文件B列名为y的列的Accuracy、Recall、Precision、F1、Pearson、<PERSON><PERSON><PERSON>\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import accuracy_score, recall_score, precision_score, f1_score\n", "gloden_file = (\n", "    \"/Users/<USER>/DetoxScore/detoxScore_for_CM_under_textdetox2024/code/data/CM/ZH.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results2/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "# column_name = \"content_score\"\n", "column_name = \"toxic_pairwise_score\"\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[column_name]\n", "    col_model = df_model[column_name]\n", "\n", "    col_gloden = [int(i * 100) for i in col_gloden]   \n", "    col_model = [int(i * 100) for i in col_model]   \n", "\n", "    # 计算各项指标\n", "    accuracy = accuracy_score(col_gloden, col_model)\n", "    recall = recall_score(col_gloden, col_model, average='macro')\n", "    precision = precision_score(col_gloden, col_model, average='macro')\n", "    f1 = f1_score(col_gloden, col_model, average='macro')\n", "\n", "    print_data.append([model_file_output, accuracy, recall, precision, f1])\n", "    \n", "# 输出为markdown格式\n", "print(\"| Prompt | Accuracy | Recall | Precision | F1 |\")\n", "print(\"| --- | --- | --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f | %.4f | %.4f |\" % (data[0], data[1], data[2], data[3], data[4]))"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["| Prompt | Accuracy | Recall | Precision | F1 |\n", "| --- | --- | --- | --- | --- |\n", "| ./results2/prompt1.csv | 0.9900 | 0.5000 | 0.4950 | 0.4975 |\n", "| ./results2/prompt10.csv | 0.9900 | 0.5000 | 0.4950 | 0.4975 |\n", "| ./results2/prompt2.csv | 0.9900 | 0.5000 | 0.4950 | 0.4975 |\n", "| ./results2/prompt3.csv | 0.9600 | 0.4848 | 0.4948 | 0.4898 |\n", "| ./results2/prompt4.csv | 0.9500 | 0.4798 | 0.4948 | 0.4872 |\n", "| ./results2/prompt5.csv | 0.9700 | 0.4899 | 0.4949 | 0.4924 |\n", "| ./results2/prompt6.csv | 0.9600 | 0.4848 | 0.4948 | 0.4898 |\n", "| ./results2/prompt7.csv | 0.9900 | 0.5000 | 0.4950 | 0.4975 |\n", "| ./results2/prompt8.csv | 0.9800 | 0.4949 | 0.4949 | 0.4949 |\n", "| ./results2/prompt9.csv | 0.9800 | 0.4949 | 0.4949 | 0.4949 |\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n", "/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/sklearn/metrics/_classification.py:1517: UndefinedMetricWarning: Precision is ill-defined and being set to 0.0 in labels with no predicted samples. Use `zero_division` parameter to control this behavior.\n", "  _warn_prf(average, modifier, f\"{metric.capitalize()} is\", len(result))\n"]}], "source": ["# 读取文件A和文件B，其中A文件为gloden标注文件，B文件为模型预测文件\n", "# 输出文件A列名为x的列和文件B列名为y的列的Accuracy、Recall、Precision、F1、Pearson、<PERSON><PERSON><PERSON>\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import accuracy_score, recall_score, precision_score, f1_score\n", "from scipy.stats import pearsonr, spearmanr\n", "gloden_file = (\n", "    \"/Users/<USER>/DetoxScore/detoxScore_for_CM_under_textdetox2024/code/data/CM/ZH.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results2/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[\"fluency_score\"]\n", "    # 推算 col_model的fluency_score 的值，fluency_score = 1 if neutral_fluency >= toxic_fluency : 0，转换为整数\n", "    col_model = np.where(df_model[\"neutral_fluency\"] >= df_model[\"toxic_fluency\"], 1, 0)\n", "    col_gloden = [int(i * 100) for i in col_gloden]   \n", "    col_model = [int(i * 100) for i in col_model]\n", "\n", "    # 计算各项指标\n", "    accuracy = accuracy_score(col_gloden, col_model)\n", "    recall = recall_score(col_gloden, col_model, average='macro')\n", "    precision = precision_score(col_gloden, col_model, average='macro')\n", "    f1 = f1_score(col_gloden, col_model, average='macro')\n", "\n", "    print_data.append([model_file_output, accuracy, recall, precision, f1])\n", "    \n", "# 输出为markdown格式\n", "print(\"| Prompt | Accuracy | Recall | Precision | F1 |\")\n", "print(\"| --- | --- | --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f | %.4f | %.4f |\" % (data[0], data[1], data[2], data[3], data[4]))"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["| Prompt | <PERSON> | <PERSON><PERSON><PERSON> |\n", "| --- | --- | --- | --- | --- |\n", "| ./results2/prompt1.csv | -0.0832 | -0.0862 |\n", "| ./results2/prompt10.csv | -0.0625 | -0.0581 |\n", "| ./results2/prompt2.csv | -0.0721 | -0.0721 |\n", "| ./results2/prompt3.csv | -0.0832 | -0.0862 |\n", "| ./results2/prompt4.csv | -0.0622 | -0.0642 |\n", "| ./results2/prompt5.csv | -0.0703 | -0.0721 |\n", "| ./results2/prompt6.csv | -0.0721 | -0.0721 |\n", "| ./results2/prompt7.csv | -0.1067 | -0.1212 |\n", "| ./results2/prompt8.csv | -0.0563 | -0.0511 |\n", "| ./results2/prompt9.csv | -0.0683 | -0.0598 |\n"]}], "source": ["from scipy.stats import pearsonr, spearmanr\n", "# 输出文件A列名为x的列和文件B列名为y的列的Pearson、<PERSON><PERSON><PERSON>\n", "print_data = []\n", "gloden_file = (\n", "    \"/Users/<USER>/DetoxScore/detoxScore_for_CM_under_textdetox2024/code/data/CM/ZH.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results2/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "column_name = \"content_score\"\n", "# column_name = \"toxic_pairwise_score\"\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[column_name]\n", "    col_model = df_model[column_name]\n", "\n", "    # 计算各项指标\n", "    pearson = pearsonr(col_gloden, col_model)[0]\n", "    spearman = spearmanr(col_gloden, col_model)[0]\n", "    print_data.append([model_file_output, pearson, spearman])\n", "\n", "# 输出为markdown格式\n", "print(\"| Prompt | Pearson | Spearman |\")\n", "print(\"| --- | --- | --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f |\" % (data[0], data[1], data[2]))"]}], "metadata": {"kernelspec": {"display_name": "fight4human", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}