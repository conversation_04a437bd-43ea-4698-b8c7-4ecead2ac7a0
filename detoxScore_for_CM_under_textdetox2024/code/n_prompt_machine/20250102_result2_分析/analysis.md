# prompt 1～10
prompt 1：最初实验用的prompt
prompt 2：整理了prompt 1的换行等格式
prompt 3：顶部介绍了释义改写任务（在做多语言文本解毒任务的时候用的这个）+ prompt 2
prompt 4：prompt 3的“释义改写”改成了“文本解毒”
prompt 5：prompt 4的基础上加入小标题
prompt 6: 让GPT-zero整理了一下的prompt 5
prompt 7: 利用prompting工程方法，CO-STAR改写了全英的prompt
prompt 8: 翻译了prompt7为中文
prompt 9: 把 prompt 8 中的“您”改为了“你”
prompt 10: 把 prompt 9 中的标签英文都改成了中文，比如 `# CONTEXT #` -> `# 背景 #`

# Accuracy、Recall、Precision表
## Content Score
| Prompt | Accuracy | Recall | Precision | F1 |
| --- | --- | --- | --- | --- |
| prompt1.csv | 0.8400 | 0.3077 | 0.3011 | 0.3043 |
| prompt2.csv | 0.8600 | 0.4725 | 0.4526 | 0.4624 |
| prompt3.csv | 0.8400 | 0.3077 | 0.3011 | 0.3043 |
| prompt4.csv | 0.8700 | 0.3187 | 0.3021 | 0.3102 |
| prompt5.csv | 0.8600 | 0.3150 | 0.3018 | 0.3082 |
| prompt6.csv | 0.8600 | 0.4725 | 0.4526 | 0.4624 |
| prompt7.csv | 0.7800 | 0.1429 | 0.1494 | 0.1461 |
| prompt8.csv | 0.7500 | 0.2747 | 0.3012 | 0.2874 |
| prompt9.csv | 0.7400 | 0.2711 | 0.3008 | 0.2852 |
| prompt10.csv | 0.7400 | 0.2711 | 0.3008 | 0.2852 |

| Prompt | Pearson | Spearman |
| --- | --- | --- |
| ./results2/prompt1.csv | -0.0832 | -0.0862 |
| ./results2/prompt10.csv | -0.0625 | -0.0581 |
| ./results2/prompt2.csv | -0.0721 | -0.0721 |
| ./results2/prompt3.csv | -0.0832 | -0.0862 |
| ./results2/prompt4.csv | -0.0622 | -0.0642 |
| ./results2/prompt5.csv | -0.0703 | -0.0721 |
| ./results2/prompt6.csv | -0.0721 | -0.0721 |
| ./results2/prompt7.csv | -0.1067 | -0.1212 |
| ./results2/prompt8.csv | -0.0563 | -0.0511 |
| ./results2/prompt9.csv | -0.0683 | -0.0598 |

## Toxic Pairwise Score
| Prompt | Accuracy | Recall | Precision | F1 |
| --- | --- | --- | --- | --- |
| prompt1.csv | 0.6200 | 0.3743 | 0.1619 | 0.1892 |
| prompt2.csv | 0.6700 | 0.3886 | 0.1786 | 0.2171 |
| prompt3.csv | 0.6400 | 0.1829 | 0.1407 | 0.1590 |
| prompt4.csv | 0.6500 | 0.1857 | 0.1413 | 0.1605 |
| prompt5.csv | 0.6500 | 0.1857 | 0.1413 | 0.1605 |
| prompt6.csv | 0.6500 | 0.1857 | 0.1429 | 0.1615 |
| prompt7.csv | 0.6900 | 0.1971 | 0.1408 | 0.1643 |
| prompt8.csv | 0.6900 | 0.4929 | 0.2586 | 0.3286 |
| prompt9.csv | 0.6900 | 0.1971 | 0.1394 | 0.1633 |
| prompt10.csv | 0.6900 | 0.2464 | 0.1760 | 0.2054 |


| Prompt | Pearson | Spearman |
| --- | --- | --- |
| ./results2/prompt1.csv | 0.1323 | 0.1487 |
| ./results2/prompt2.csv | 0.2830 | 0.3120 |
| ./results2/prompt3.csv | 0.0173 | 0.0375 |
| ./results2/prompt4.csv | 0.0314 | 0.0626 |
| ./results2/prompt5.csv | 0.0165 | 0.0601 |
| ./results2/prompt6.csv | 0.0636 | 0.1045 |
| ./results2/prompt7.csv | 0.3014 | 0.1079 |
| ./results2/prompt8.csv | 0.1437 | 0.0491 |
| ./results2/prompt9.csv | -0.0595 | -0.0648 |
| ./results2/prompt10.csv | 0.0161 | 0.0445 |

## Fluency Score
| Prompt | Accuracy | Recall | Precision | F1 |
| --- | --- | --- | --- | --- |
| prompt1.csv | 0.9900 | 0.5000 | 0.4950 | 0.4975 |
| prompt2.csv | 0.9900 | 0.5000 | 0.4950 | 0.4975 |
| prompt3.csv | 0.9600 | 0.4848 | 0.4948 | 0.4898 |
| prompt4.csv | 0.9500 | 0.4798 | 0.4948 | 0.4872 |
| prompt5.csv | 0.9700 | 0.4899 | 0.4949 | 0.4924 |
| prompt6.csv | 0.9600 | 0.4848 | 0.4948 | 0.4898 |
| prompt7.csv | 0.9900 | 0.5000 | 0.4950 | 0.4975 |
| prompt8.csv | 0.9800 | 0.4949 | 0.4949 | 0.4949 |
| prompt9.csv | 0.9800 | 0.4949 | 0.4949 | 0.4949 |
| prompt10.csv | 0.9900 | 0.5000 | 0.4950 | 0.4975 |


