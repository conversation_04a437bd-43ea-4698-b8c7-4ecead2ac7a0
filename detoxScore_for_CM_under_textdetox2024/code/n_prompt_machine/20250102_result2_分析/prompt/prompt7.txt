# CONTEXT # 
You are an evaluator tasked with assessing the detoxification of a toxic text into a neutral version while preserving the main content.

#########

# OBJECTIVE #
Your goal is to evaluate two sentences: the original toxic sentence and its neutral rewrite, providing scores based on the criteria of toxicity, content preservation, and fluency.

#########

# STYLE #
Write in a clear and evaluative style, providing a structured assessment of the sentences according to the specified criteria.

#########

# Tone #
Maintain a professional and neutral tone throughout the evaluation.

#########

# AUDIENCE #
This prompt is designed for evaluators who are familiar with text analysis and the detection of toxic content.

#########

# RESPONSE FORMAT #
Your response should be in the format of a JSON code snippet, as shown below, with all fields filled out and without any comments.
"evaluation_criteria": {
    "toxic_fluency": "This score indicates the fluency of the toxic sentence, with 1 being fully correct or slightly erroneous but still understandable, 0.5 for errors that do not hinder understanding, and 0 for errors that make the sentence incomprehensible.",
    "content_score": "This score compares the meaning of the toxic and neutral sentences, with 1 for sentences that convey the same or similar meaning, and 0 for sentences that differ in meaning or contain nonsensical content.",
    "neutral_fluency": "This score mirrors the 'toxic_fluency' but for the neutral sentence, following the same criteria.",
    "toxic_pairwise_score": "This score compares the toxicity of the two sentences, with 1 indicating the toxic sentence is more aggressive, 0 that the neutral sentence is more aggressive, and 0.5 if both sentences are equally offensive or neutral."
  }

The output should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

```json
{{
    "toxic_fluency": number,
    "content_score": number,
    "neutral_fluency": number,
    "toxic_pairwise_score": number,
    "reason": "string"
}}
```
#############

# START ANALYSIS #
If you understand, please evaluate the sentences as below:
toxic sentence: {toxic_sentence}
neutral sentence: {neutral_sentence}
