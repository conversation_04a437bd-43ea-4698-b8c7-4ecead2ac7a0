现在介绍文本解毒任务：
任务描述：给定一段有毒的文本，以无毒的方式重写它，同时尽可能保留主要内容。旨在呈现句子的中立版本，保留有意义的内容。
处理方案：仅处理显性类型的毒性（明显存在淫秽和粗鲁的词汇，但仍然存在有意义的中立内容），而不处理隐性类型，例如讽刺、被动攻击性或对某些群体的直接仇恨找不到中性内容的地方。
评价指标：
- 指标toxic_pairwise_score：指标toxic_pairwise_score是选择句子1和句子2哪个句子更具攻击性，有下面三类：
    （1）toxic_pairwise_score=1：句子1 更具攻击性。
    （2）toxic_pairwise_score=0：句子2 更具攻击性。
    （3）toxic_pairwise_score=0.5：这两个句子的冒犯性完全相同（例如，两个句子完全中立或完全冒犯）
- 指标content_score: 指标content_score指出句子1和句子2的意思是否相同（或接近），这两个句子不必完全相同，其中一个句子可以具有攻击性，而另一个句子则是用中性的语气表达同样的意思。可分为两类：
    （1）content_score=1: 句子1和句子2的意思是否相同（或接近）。
    （2）content_score=0: 句子1和句子2的意思不相同，或者句子1和句子2的意思不相同不接近接近, 或者一个或两个句子都包含无意义的话（非词语、无意义的词语串等）。
- 指标fluency：指标fluency可分为三类：
    （1）句子完全正确或有轻微错误，设置 fluency=1。如果句子写得完全正确，用词正确且恰当。此外，我们不处理标点符号错误--我们允许逗号、句号或它们出现在句子的错误位置，设置 fluency=1。我们也会出现词的大小写错误--句子可能以小写字母开头，名字也可能以小写字母书写，或者一个单词可能突然以大写字母开头，设置 fluency=1。只有标点符号错误或大小写错误，设置 fluency=1
    （2）fluency=0.5：句子有错误，但不影响理解。如果文中有拼写错误--单词的形式不恰当、粘连在一起、拼写错误--但仍能完全理解文章的意思。
    （3）fluency=0：句子有一些错误使您无法理解其含义。如果文字歪曲、使用完全不恰当，还有一些奇怪的符号，无法理解全文或部分内容的意思。
- 指标toxic_fluency：根据指标fluency，设置toxic_fluency为句子1对应指标fluency的结果
- 指标neutral_fluency：根据指标fluency，设置neutral_fluency为句子2对应指标fluency的结果

你现在是评估有毒文本改写为无毒文本任务的工作人员，请你评估句子1和句子2，并给出评价结果：
句子1: {toxic_sentence}
句子2: {neutral_sentence}
The output should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

```json
{{
    "toxic_fluency": number,
    "content_score": number,
    "neutral_fluency": number,
    "toxic_pairwise_score": number,
    "reason": "string" // 你的理由
}}
```
你返回的 JSON 结果移除所有的注释，并确保所有的字符串都用双引号括起来。