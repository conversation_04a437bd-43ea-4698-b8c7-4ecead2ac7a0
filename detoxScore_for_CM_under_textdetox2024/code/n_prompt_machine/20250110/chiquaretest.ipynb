{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "The internally computed table of expected frequencies has a zero element at (0, 0).", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 12\u001b[0m\n\u001b[1;32m      9\u001b[0m contingency_table \u001b[38;5;241m=\u001b[39m np\u001b[38;5;241m.\u001b[39marray([A, B])\n\u001b[1;32m     11\u001b[0m \u001b[38;5;66;03m# 进行卡方检验\u001b[39;00m\n\u001b[0;32m---> 12\u001b[0m chi2, p, dof, expected \u001b[38;5;241m=\u001b[39m \u001b[43mchi2_contingency\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcontingency_table\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;66;03m# 准备markdown格式的结果\u001b[39;00m\n\u001b[1;32m     15\u001b[0m result_md \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\"\"\u001b[39m\n\u001b[1;32m     16\u001b[0m \u001b[38;5;124m| 分布 | 类别0 | 类别1 | 类别2 |\u001b[39m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;124m| ---- | ---- | ---- | ---- |\u001b[39m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     24\u001b[0m \u001b[38;5;124m- 自由度: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mdof\u001b[38;5;132;01m}\u001b[39;00m\n\u001b[1;32m     25\u001b[0m \u001b[38;5;124m\"\"\"\u001b[39m\n", "File \u001b[0;32m/opt/homebrew/Caskroom/miniforge/base/envs/fight4human/lib/python3.9/site-packages/scipy/stats/contingency.py:340\u001b[0m, in \u001b[0;36mchi2_contingency\u001b[0;34m(observed, correction, lambda_)\u001b[0m\n\u001b[1;32m    336\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m np\u001b[38;5;241m.\u001b[39many(expected \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m):\n\u001b[1;32m    337\u001b[0m     \u001b[38;5;66;03m# Include one of the positions where expected is zero in\u001b[39;00m\n\u001b[1;32m    338\u001b[0m     \u001b[38;5;66;03m# the exception message.\u001b[39;00m\n\u001b[1;32m    339\u001b[0m     zeropos \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mzip\u001b[39m(\u001b[38;5;241m*\u001b[39mnp\u001b[38;5;241m.\u001b[39mnonzero(expected \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m)))[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m--> 340\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThe internally computed table of expected \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    341\u001b[0m                      \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mfrequencies has a zero element at \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mzeropos\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    343\u001b[0m \u001b[38;5;66;03m# The degrees of freedom\u001b[39;00m\n\u001b[1;32m    344\u001b[0m dof \u001b[38;5;241m=\u001b[39m expected\u001b[38;5;241m.\u001b[39msize \u001b[38;5;241m-\u001b[39m \u001b[38;5;28msum\u001b[39m(expected\u001b[38;5;241m.\u001b[39mshape) \u001b[38;5;241m+\u001b[39m expected\u001b[38;5;241m.\u001b[39mndim \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m1\u001b[39m\n", "\u001b[0;31mValueError\u001b[0m: The internally computed table of expected frequencies has a zero element at (0, 0)."]}], "source": ["from scipy.stats import chi2_contingency\n", "import numpy as np\n", "\n", "# 示例数据\n", "A = np.array([1,1])  # 假设这是模型的标注分布\n", "B = np.array([1,1])  # 假设这是人类的标注分布\n", "\n", "# 构建列联表\n", "contingency_table = np.array([A, B])\n", "\n", "# 进行卡方检验\n", "chi2, p, dof, expected = chi2_contingency(contingency_table)\n", "\n", "# 准备markdown格式的结果\n", "result_md = f\"\"\"\n", "| 分布 | 类别0 | 类别1 | 类别2 |\n", "| ---- | ---- | ---- | ---- |\n", "| 模型标注 | {A[0]} | {A[1]} | {A[2]} |\n", "| 人类标注 | {B[0]} | {B[1]} | {B[2]} |\n", "| 期望频数 | {expected[0,0]:.2f} | {expected[0,1]:.2f} | {expected[0,2]:.2f} |\n", "\n", "- 卡方统计量: {chi2:.2f}\n", "- p值: {p:.4f}\n", "- 自由度: {dof}\n", "\"\"\"\n", "\n", "print(result_md)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["| 分布 | 类别0 | 类别1 | 类别2 |\n", "| ---- | ---- | ---- | ---- |\n", "| 模型标注 | 50 | 30 | 20 |\n", "| 人类标注 | 45 | 25 | 30 |\n", "| 期望频数 | 47.50 | 27.50 | 25.00 |\n", "\n", "- 卡方统计量: 2.72\n", "- p值: 0.2570\n", "- 自由度: 2"]}], "metadata": {"kernelspec": {"display_name": "fight4human", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}