# 测试例子
## 1. 例子 假阴性
	
句子1：黑河南、黑北方的都是南满

句子2：批评河南、批评北方的人都是南方人

大模型：0 句子1表达的是对某人的称呼或身份的疑问，而句子2表达的是对黑人的态度和看待方式的问题，两者语义不同。

人工：1

句子1：黑河南、黑北方的都是南满

句子2：批评河南、批评北方的人都是南方人

### 针对性设计 prompt1

在不考虑语言色彩的情况下，句子1和句子2表达意思相同吗？

- 一句话中突然出现一个“黑”字，有“批评”的意思。
- 一句话中突然出现一个“南满”字，也有“南方人”的意思。

非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。


输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```
prompt1 的方法也是结合了prompt2的结果，三个prompt结果中，只要有一个认为是讲的同一个意思，那么就是1，否则就是0。

## 2. 例子 假阴性
### 针对性设计 prompt2
	
句子1：我媳妇也会打拳！??

句子2：我老婆也懂得极端主义！！？

大模型：0 句子1表达的是媳妇会打拳，这是一种技能；句子2表达的是老婆懂得极端主义，这是一种思想或信仰。两者所表达的内容和侧重点不同，因此不相同。

人工：1

句子1是代表着互联网或者生活中带有黑话、污言秽语等文本

句子1：我媳妇也会打拳！??

句子2：我老婆也懂得极端主义！！？

在不考虑语言色彩的情况下，句子1和句子2表达意思相同吗？

- 一句话中出现“某某打拳”这个说法，可能代表着“某某是极端女权主义者”。

非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。


输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```

# 结果
之前最好：20250113的prompt2 0.1322
目标超过：0.14 完成
目前 0.2440
| Prompt | Pearson | Spearman |
| --- | --- | --- |
| ./results/prompt1.csv | 0.1152 | 0.1152 |
| ./results/prompt2.csv | 0.2440 | 0.2440 |
| 将14号的最好的prompt2和13号的结合| 0.2593 | 0.2593 |

人类标注数据：
0: 9
1: 91
## prompt1
0: 28
1: 72
## prompt2
0: 16
1: 84
## prompt2+13prompt
0: 15
1: 85