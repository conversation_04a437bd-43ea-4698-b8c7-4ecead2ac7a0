<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
<\任务背景介绍>

<任务介绍>
你的任务是判断句子1和句子2背后表达的意思是否相同。
- 不考虑语气和情感上的差异
- 句子1是代表着互联网或者生活中带有黑话、污言秽语等文本
- “某某打拳”这个说法，也有代表着“某某参与激进的性别平等讨论”。
<\任务介绍>

<评价指标>
content_score：非常可能相同为5分，可能相同为4分，无法判断为3分，可能不相同为2分，非常可能不相同为1分。
<\评价指标>

<你需要评价的数据> 
句子1：丑的一批、呸
	
句子2：外貌并不出众，真是遗憾
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": string
}
```
<\输出数据格式> 


<你需要评价的数据> 
句子1：我媳妇也会打拳！??	

句子2：我的妻子也会参与一些激进的性别平等讨论！??
<\你需要评价的数据>