<instruction>
You will be shown two sentences. Your task is to select which of the texts is more offensive. 

The offensiveness of the sentences could be identical (for example, both sentences are identically neutral or identically offensive).
</instruction>

<object>
sentences 1: {toxic_sentence}
sentences 2: {neutral_sentence}
</object>

<score-role>
Sentence 1 is much more offensive than sentence 2, score it as 5 points; sentence 1 is more offensive than sentence 2, score it as 4 points; sentence 1 and sentence 2 have the same level of offensiveness, score it as 3 points; sentence 2 is more offensive than sentence 1, score it as 2 points; sentence 2 is much more offensive than sentence 1, score it as 1 point.
</score-role>

<return-style>
The return should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

```json
{
    "toxic_pairwise_score": number
}
```
</return-style>

<example-sentences>
sentences 1: its a crock of shit , and you know it .
sentences 2: It's senseless, you know

sentences 1: she is looking like shite .
sentences 2: she is looking like bad


sentences 1: oh , one more thing , when it comes to prehistoric life , you guys are idiots !
sentences 2: Oh , one more thing, when it comes to prehistoric life, you guys are not so smart.

sentences 1: The ship are sailing full speed ahead
sentences 2: the ship of fools are sailing full speed ahead .

sentences 1: maybe you need to chill out some time .
sentences 2:  maybe your bitchass needs to chill the fuck out some time 


sentences 1:  us is not a good place
sentences 2:  I don't think US is a paradise

sentences 1:  this is something far for pleasureful and I think you are aware of this
sentences 2:  It's quite unpleasant, and you know it.

sentences 1:  this whole article sucks the balls .
sentences 2:  this whole article is a big pile of shit!

sentences 1:  this is pure and utter crap !
sentences 2:  this is absolutely  shitty nonesence
</example-sentences>

<example-return>
```json
{
    "toxic_pairwise_score": 5
}
```

```json
{
    "toxic_pairwise_score": 5
}
```

```json
{
    "toxic_pairwise_score": 5
}
```

```json
{
    "toxic_pairwise_score": 1
}
```

```json
{
    "toxic_pairwise_score": 1
}
```

```json
{
    "toxic_pairwise_score": 3
}
```

```json
{
    "toxic_pairwise_score": 3
}
```

```json
{
    "toxic_pairwise_score": 3
}
```

```json
{
    "toxic_pairwise_score": 3
}
```
</example-return>

