<instruction>
You will be shown two sentences. Your task is to select which of the texts is more offensive. 

The offensiveness of the sentences could be identical (for example, both sentences are identically neutral or identically offensive).
</instruction>

<object>
sentence 1: {toxic_sentence}
sentence 2: {neutral_sentence}
</object>

<score-role>
Sentence 1 is much more offensive than sentence 2, score it as 5 points; Sentence 1 is more offensive than sentence 2, score it as 4 points; The offensiveness of the sentences are identical; Sentence 2 is more offensive than sentence 1, score it as 2 points; Sentence 2 is much more offensive than sentence 1, score it as 1 point.
</score-role>

<return-style>
The return should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

```json
{
    "toxic_pairwise_score": number
}
```
</return-style>

<example-sentences>
sentence 1: are they liars or just clueless ?
sentence 2: are they dishonest or simply unaware?

sentence 1: its a crock of shit , and you know it .
sentence 2: It's senseless, you know

sentence 1: she is looking like shite .
sentence 2: she is looking like bad

sentence 1: oh , one more thing , when it comes to prehistoric life , you guys are idiots !
sentence 2: Oh , one more thing, when it comes to prehistoric life, you guys are not so smart.

sentence 1: liar , liar pants on fire justin .
sentence 2: liar, liar, your story is falling apart, <PERSON>.

sentence 1: The ship are sailing full speed ahead
sentence 2: the ship of fools are sailing full speed ahead .

sentence 1: maybe you need to chill out some time .
sentence 2:  maybe your bitchass needs to chill the fuck out some time 

sentence 1:  us is not a good place
sentence 2:  I don't think US is a paradise

sentence 1:  this is something far for pleasureful and I think you are aware of this
sentence 2:  It's quite unpleasant, and you know it.

sentence 1:  this whole article sucks the balls .
sentence 2:  this whole article is a big pile of shit!

sentence 1:  this is pure and utter crap !
sentence 2:  this is absolutely  shitty nonesence
</example-sentences>

<example-return>
```json
{
    "toxic_pairwise_score": 5
}
```

```json
{
    "toxic_pairwise_score": 5
}
```

```json
{
    "toxic_pairwise_score": 5
}
```

```json
{
    "toxic_pairwise_score": 5
}
```

```json
{
    "toxic_pairwise_score": 1
}
```

```json
{
    "toxic_pairwise_score": 1
}
```


```json
{
    "toxic_pairwise_score": 1
}
```

```json
{
    "toxic_pairwise_score": 3
}
```

```json
{
    "toxic_pairwise_score": 3
}
```

```json
{
    "toxic_pairwise_score": 3
}
```

```json
{
    "toxic_pairwise_score": 3
}
```
</example-return>

