You will be shown two sentences. Your task is to select which of the texts is more offensive. 

The offensiveness of the sentences could be identical (for example, both sentences are identically neutral or identically offensive).

sentences 1: {toxic_sentence}
sentences 2: {neutral_sentence}

IF sentence 1 is more offensive than sentence 2, toxic_pairwise_score is 1; 
ELSE IF sentence 2 is more offensive than sentence 2, toxic_pairwise_score is 0; 
ELSE the offensiveness of the sentences could be identical (for example, both sentences are identically neutral or identically offensive), toxic_pairwise_score is 0.5; 

The return should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

```json
{
    "toxic_pairwise_score": number
}
```

<example-sentences>
sentences 1: oh , one more thing , when it comes to prehistoric life , you guys are idiots !
sentences 2: Oh , one more thing, when it comes to prehistoric life, you guys are not so smart.

sentences 1: maybe you need to chill out some time .
sentences 2:  maybe your bitchass needs to chill the fuck out some time 

sentences 1:  us is not a good place
sentences 2:  I don't think US is a paradise

sentences 1:  this is something far for pleasureful and I think you are aware of this
sentences 2:  It's quite unpleasant, and you know it.
</example-sentences>

<example-return>
```json
{
    "content_score": 1
}
```

```json
{
    "content_score": 0
}
```

```json
{
    "content_score": 0.5
}
```
</example-return>

