<instruction>
You will be shown two sentences. Your task is to select which of the texts is more offensive. 

The offensiveness of the sentences could be identical (for example, both sentences are identically neutral or identically offensive).
</instruction>

<object>
sentences 1: {toxic_sentence}
sentences 2: {neutral_sentence}
</object>

<toxic_pairwise_score-role>
IF sentence 1 is more offensive than sentence 2, toxic_pairwise_score is 1; 
ELSE IF sentence 2 is more offensive than sentence 2, toxic_pairwise_score is 0; 
ELSE the offensiveness of the sentences could be identical (for example, both sentences are identically neutral or identically offensive), toxic_pairwise_score is 0.5; 
</score-role>

<return-style>
The return should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

```json
{
    "toxic_pairwise_score": number
}
```
</return-style>