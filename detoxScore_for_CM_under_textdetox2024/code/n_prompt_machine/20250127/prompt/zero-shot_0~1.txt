You will be shown two sentences. Your task is to select which of the texts is more offensive. 

The offensiveness of the sentences could be identical (for example, both sentences are identically neutral or identically offensive).

sentences 1: {toxic_sentence}
sentences 2: {neutral_sentence}

The score range you are given is [0,1]. If you feel that sentence 1 is more toxic, then the score should be closer to 1. If you feel that sentence 2 is more toxic, then the score should be closer to 0. If you feel that both sentences have the same level of toxicity, then your score should be 0.5.

The return should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

```json
{
    "toxic_pairwise_score": number
}
```