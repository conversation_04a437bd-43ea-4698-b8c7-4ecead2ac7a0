
<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
<\任务背景介绍>

<任务介绍>
你的任务是评价两个句子，判断句子1和句子2表达意思是否相同。判断时，要仔细剖析句子所表达的核心事件、核心关注点，重点关注它们是否一致。这要求你仔细分析句子的核心语义，确保判断的准确性，避免出现误判情况。哪怕句子中有部分表述相似，但核心的关键事件不同，也应判断为意思不相同。请尤其注意，即使两个句子有部分表述相同，但核心事件差异明显时，也应判断为意思不相同。
<\任务介绍>

<评价指标>
content_score：判断句子1和句子2表达意思是否相同，非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。
<\评价指标>

<注意事项>
1、请务必严格忽略句子中的语言色彩，重点关注核心意思，这是确保正确完成本任务的关键所在。
<\注意事项>

<你需要评价的数据> 
句子1：{toxic_sentence}

句子2：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number
}
```
<\输出数据格式> 

<样本>
例如，“小明吃了苹果”和“苹果被小明吃了”，核心事件都是小明吃苹果，表达意思相同；而“小明吃了苹果”和“小红吃了香蕉”，核心事件不同，表达意思不相同；“他想去公园玩”和“他想在家看电视”，核心事件不同，表达意思也不相同。另外，像句子1：你这个坏人别再搞破坏了；句子2：请你文明一些。这两个句子虽都有对对方行为的一种提醒，但核心事件不同，应判断为意思不相同，评分为2分。当句子1为“Thanks for being so rude when I offered help.”，句子2为“Thank you for your response, though it wasn't useful when I was trying to assist.”，句子1有指责对方粗鲁的意味，句子2只是说明对方回应没帮助，核心语义不同，表达意思不相同，也应评为2分。
以下是不同分数对应的具体示例：
```json
{
    "content_score": 5
}
```
当句子1为“Apple is a fruit.”，句子2为“Apple belongs to the category of fruits.”，两个句子表达意思非常相同，可评为5分。
```json
{
    "content_score": 4
}
```
当句子1为“He is happy.”，句子2为“He feels delighted.”，两个句子表达意思相同，可评为4分。
当句子1为 “Stop supporting thieves.”，句子2为 “Stop supporting criminals.”，“thieves” 属于 “criminals” 的范畴，两个句子表达意思相同，同样可评为4分。
当句子1为 “XXXX was born a DICK.”，句子2为 “XXX has always been known for a particular behavior.”，两个句子表达意思相同，可评为4分。
当句子1为 “They amins are such bastards.”，句子2为 “They people are being unfair.”，表达类似负面态度，意思相同，可评为4分。
当句子1为 “this is a joke, are we all fucking retards?”，句子2为 “this is a joke, are we all joking?”，语气不同但核心语义相同，意思相同，可评为4分。
```json
{
    "content_score": 2
}
```
当句子1为“She likes reading books.”，句子2为“She enjoys watching movies.”，两个句子表达意思不相同，可评为2分。
当句子1为 “no fucking way number bricks fits in a box, this is bs.”，句子2为 “No way that number of bricks fits in a box, this is incorrect.”，虽部分表述相似但核心语义不同，表达意思不相同，可评为2分。
当句子1为 “oh shit, nooooo, is someone going to spank him now?”，句子2为 “Oh no, is there going to be a consequence for his actions now?”，表面有一定关联但实际核心语义不同，表达意思不相同，可评为2分。
```json
{
    "content_score": 1
}
```
当句子1为 “shit, she d love an number pm sunset.”，句子2为 “She would really appreciate a picturesque sunset around that time.”，两个句子表达意思非常不相同，可评为1分。
比如“他真该死”和“他要为自己的行为负责”，虽前一句语言色彩激烈，但核心意思与后一句相近，应判断为意思相同；“你这个蠢货别说话了” 和 “请你安静一下”，同样应判断为意思相同；“You fools are so annoying.” 和 “You guys are being bothersome.”，也应判断为意思相同；“他是个混蛋”和“他做事让人不满意”，同样核心意思相近，要判断为意思相同。此外，像句子1‘给我闭嘴，你这个白痴’和句子2‘请你保持安静’，虽句子1语言色彩激烈，但核心意思都是希望对方安静，应判断为意思相同；句子1‘你这废物什么都做不好’和句子2‘你做事的效果不太理想’，尽管句子1带有强烈负面色彩，但核心都是在表达对对方做事结果的不满，应判断为意思相同。
</样本>