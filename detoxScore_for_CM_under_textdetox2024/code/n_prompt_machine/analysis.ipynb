{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["etd_zero_shot_results_en_ENPrompt.csv\n", "| Prompt | <PERSON> | <PERSON><PERSON><PERSON> |\n", "| --- | --- | --- |\n", "| ./results/etd_zero_shot_results_en_ENPrompt.csv | 0.3637 | 0.3926 |\n"]}], "source": ["\n", "\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.stats import pearsonr, spearmanr\n", "# 输出文件A列名为x的列和文件B列名为y的列的Pearson、<PERSON><PERSON><PERSON>\n", "print_data = []\n", "gloden_file = (\n", "    \"../../../TDE/EN.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "column_name = \"STA\"\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    print(model_file_output)\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[column_name]\n", "    col_model = df_model[column_name]\n", "\n", "    # 计算各项指标\n", "    pearson = pearsonr(col_gloden, col_model)[0]\n", "    spearman = spearmanr(col_gloden, col_model)[0]\n", "    print_data.append([model_file_output, pearson, spearman])\n", "\n", "# 输出为markdown格式 按照 data[1] 降序排列\n", "# 按照 data[1] 降序排列\n", "print_data.sort(key=lambda x: x[1], reverse=True)\n", "\n", "print(\"| Prompt | Pearson | Spearman |\")\n", "print(\"| --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f |\" % (data[0], data[1], data[2]))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["etd_zero_shot_results_en_ENPrompt.csv\n", "| Prompt | <PERSON> | <PERSON><PERSON><PERSON> |\n", "| --- | --- | --- |\n", "| ./results/etd_zero_shot_results_en_ENPrompt.csv | 0.3637 | 0.3926 |\n"]}], "source": ["\n", "\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.stats import pearsonr, spearmanr\n", "# 输出文件A列名为x的列和文件B列名为y的列的Pearson、<PERSON><PERSON><PERSON>\n", "print_data = []\n", "gloden_file = (\n", "    \"../../../TDE/EN.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "column_name = \"j_score\"\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    print(model_file_output)\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[column_name]\n", "    col_model = df_model[column_name]\n", "\n", "    # 计算各项指标\n", "    pearson = pearsonr(col_gloden, col_model)[0]\n", "    spearman = spearmanr(col_gloden, col_model)[0]\n", "    print_data.append([model_file_output, pearson, spearman])\n", "\n", "# 输出为markdown格式 按照 data[1] 降序排列\n", "# 按照 data[1] 降序排列\n", "print_data.sort(key=lambda x: x[1], reverse=True)\n", "\n", "print(\"| Prompt | Pearson | Spearman |\")\n", "print(\"| --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f |\" % (data[0], data[1], data[2]))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["etd_zero_shot_results_en_ENPrompt.csv\n", "| Prompt | <PERSON> | <PERSON><PERSON><PERSON> |\n", "| --- | --- | --- |\n", "| ./results/etd_zero_shot_results_en_ENPrompt.csv | 0.3637 | 0.3926 |\n"]}], "source": ["\n", "\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.stats import pearsonr, spearmanr\n", "# 输出文件A列名为x的列和文件B列名为y的列的Pearson、<PERSON><PERSON><PERSON>\n", "print_data = []\n", "gloden_file = (\n", "    \"../../../TDE/EN.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "column_name = \"j_score\"\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    print(model_file_output)\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[column_name]\n", "    col_model = df_model[column_name]\n", "\n", "    # 计算各项指标\n", "    pearson = pearsonr(col_gloden, col_model)[0]\n", "    spearman = spearmanr(col_gloden, col_model)[0]\n", "    print_data.append([model_file_output, pearson, spearman])\n", "\n", "# 输出为markdown格式 按照 data[1] 降序排列\n", "# 按照 data[1] 降序排列\n", "print_data.sort(key=lambda x: x[1], reverse=True)\n", "\n", "print(\"| Prompt | Pearson | Spearman |\")\n", "print(\"| --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f |\" % (data[0], data[1], data[2]))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["etd_zero_shot_results_en_ENPrompt.csv\n", "| Prompt | <PERSON> | <PERSON><PERSON><PERSON> |\n", "| --- | --- | --- |\n", "| ./results/etd_zero_shot_results_en_ENPrompt.csv | 0.3637 | 0.3926 |\n"]}], "source": ["\n", "\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.stats import pearsonr, spearmanr\n", "# 输出文件A列名为x的列和文件B列名为y的列的Pearson、<PERSON><PERSON><PERSON>\n", "print_data = []\n", "gloden_file = (\n", "    \"../../../TDE/EN.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "column_name = \"j_score\"\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    print(model_file_output)\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[column_name]\n", "    col_model = df_model[column_name]\n", "\n", "    # 计算各项指标\n", "    pearson = pearsonr(col_gloden, col_model)[0]\n", "    spearman = spearmanr(col_gloden, col_model)[0]\n", "    print_data.append([model_file_output, pearson, spearman])\n", "\n", "# 输出为markdown格式 按照 data[1] 降序排列\n", "# 按照 data[1] 降序排列\n", "print_data.sort(key=lambda x: x[1], reverse=True)\n", "\n", "print(\"| Prompt | Pearson | Spearman |\")\n", "print(\"| --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f |\" % (data[0], data[1], data[2]))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["etd_zero_shot_results_en_ENPrompt.csv\n", "| Prompt | <PERSON> | <PERSON><PERSON><PERSON> |\n", "| --- | --- | --- |\n", "| ./results/etd_zero_shot_results_en_ENPrompt.csv | 0.3637 | 0.3926 |\n"]}], "source": ["\n", "\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.stats import pearsonr, spearmanr\n", "# 输出文件A列名为x的列和文件B列名为y的列的Pearson、<PERSON><PERSON><PERSON>\n", "print_data = []\n", "gloden_file = (\n", "    \"../../../TDE/EN.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "column_name = \"j_score\"\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    print(model_file_output)\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[column_name]\n", "    col_model = df_model[column_name]\n", "\n", "    # 计算各项指标\n", "    pearson = pearsonr(col_gloden, col_model)[0]\n", "    spearman = spearmanr(col_gloden, col_model)[0]\n", "    print_data.append([model_file_output, pearson, spearman])\n", "\n", "# 输出为markdown格式 按照 data[1] 降序排列\n", "# 按照 data[1] 降序排列\n", "print_data.sort(key=lambda x: x[1], reverse=True)\n", "\n", "print(\"| Prompt | Pearson | Spearman |\")\n", "print(\"| --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f |\" % (data[0], data[1], data[2]))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["etd_zero_shot_results_en_ENPrompt.csv\n", "| Prompt | <PERSON> | <PERSON><PERSON><PERSON> |\n", "| --- | --- | --- |\n", "| ./results/etd_zero_shot_results_en_ENPrompt.csv | 0.3637 | 0.3926 |\n"]}], "source": ["\n", "\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.stats import pearsonr, spearmanr\n", "# 输出文件A列名为x的列和文件B列名为y的列的Pearson、<PERSON><PERSON><PERSON>\n", "print_data = []\n", "gloden_file = (\n", "    \"../../../TDE/EN.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "column_name = \"j_score\"\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    print(model_file_output)\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[column_name]\n", "    col_model = df_model[column_name]\n", "\n", "    # 计算各项指标\n", "    pearson = pearsonr(col_gloden, col_model)[0]\n", "    spearman = spearmanr(col_gloden, col_model)[0]\n", "    print_data.append([model_file_output, pearson, spearman])\n", "\n", "# 输出为markdown格式 按照 data[1] 降序排列\n", "# 按照 data[1] 降序排列\n", "print_data.sort(key=lambda x: x[1], reverse=True)\n", "\n", "print(\"| Prompt | Pearson | Spearman |\")\n", "print(\"| --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f |\" % (data[0], data[1], data[2]))\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.22"}}, "nbformat": 4, "nbformat_minor": 2}