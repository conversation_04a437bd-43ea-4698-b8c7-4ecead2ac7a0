import pandas as pd
import os
import uuid
from datetime import datetime
import re
import json


def extract_json_from_string(input_string):
    # 使用正则表达式匹配 JSON 部分
    json_match = re.search(r"\{.*\}", input_string, re.DOTALL)
    if json_match:
        try:
            json_str = json_match.group(0)
            # 将 JSON 字符串中的双引号替换为单引号
            json_str = json_str.replace('""', '"')
            # 将 JSON 字符串转换为字典
            json_dict = json.loads(json_str)
            return json_dict
        except json.JSONDecodeError:
            return {"reason": input_string}
    else:
        return {"reason": input_string}


# Define input and output paths
input_file = "./dataset/未命名_V3_dataset_20250328005730.xlsx"
output_dir = "./output"
os.makedirs(output_dir, exist_ok=True)

# Read the Excel file
df = pd.read_excel(input_file)

# Extract the required columns
columns_to_extract = ["toxic_sentence", "neutral_sentence", "模型回答"]
# Extract 'content_score' from '模型回答' and add it as a new column
df["content_score"] = df["模型回答"].apply(
    lambda x: extract_json_from_string(x).get("content_score", None)
)
columns_to_extract.append("content_score")
output_df = df[columns_to_extract]

# Define output file name
id = str(uuid.uuid4())[:8]  # Generate a short unique ID
name = os.path.splitext(os.path.basename(input_file))[
    0
]  # Use the input file name without extension
output_file = os.path.join(output_dir, f"{id}_{name}.csv")

# Save to CSV
output_df.to_csv(output_file, index=False, encoding="utf-8-sig")

print(f"File saved to {output_file}")
