<任务背景介绍>
The text detoxification model can convert a toxic sentence into a non-toxic sentence. Non toxic sentences can maintain sentence fluency, non toxicity, and convey the same neutral meaning as toxic sentences.
For crowdsourcing workers evaluating the performance of text detoxification models, they will see two sentences. One of the sentences contains offensive and harmful content, while the other is a general statement about the same meaning.
<\任务背景介绍>

<任务介绍>
You will be shown two sentences. Your task is to indicate whether they mean the same (or close) thing or not.
The sentences don't have to be identical - one of them can be offensive and the other one can say the same thing in a neutral tone.
<\任务介绍>

<评价指标>
0 <= content_score <= 1, more close to 1, mean more convey the same meaning.
<\评价指标>

<你需要评价的数据> 
句子1：{toxic_sentence}

句子2：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": float,
    "reason" string
}
```
<\输出数据格式> 