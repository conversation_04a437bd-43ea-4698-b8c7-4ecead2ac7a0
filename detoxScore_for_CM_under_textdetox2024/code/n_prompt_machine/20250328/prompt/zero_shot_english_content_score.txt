<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
<\任务背景介绍>

<任务介绍>
You will be shown two sentences. Your task is to indicate whether they mean the same (or close) thing or not.
The sentences don't have to be identical - one of them can be offensive and the other one can say the same thing in a neutral tone.

If one or both sentences contain nonsense (non-words, senseless chains of words, etc.), choose the option "No".
<\任务介绍>

<评价指标>
content_score：
1: they mean the same (or close) thing
0.5: No:one or both sentences contain nonsense (non-words, senseless chains of words, etc.)
0: they mean not the same (or close)
<\评价指标>

<你需要评价的数据> 
句子1：{toxic_sentence}

句子2：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason" string
}
```
<\输出数据格式> 