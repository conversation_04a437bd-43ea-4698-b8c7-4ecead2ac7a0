<任务背景>
自然语言处理中的文本解毒模型（Text Detoxification Model）是指一种通过文本风格迁移技术，将含有显性毒性内容（如粗俗、侮辱性词汇）的文本转换为语义等效的非毒性表达，同时最大限度保留原始语义内容的生成模型。其核心特征包括：

​任务定义：
输入：包含显性毒性（如脏话、攻击性词汇）的多语言文本
输出：在目标语言中保持语义一致的非毒性改写文本
处理范围：明确排除隐式毒性（如讽刺、被动攻击），专注于可通过词汇替换解决的显性毒性

评估体系：
三维评价指标：
风格转换准确率（STA）：毒性消除程度（0-1）
内容保留度（SIM）：LaBSe嵌入的余弦相似度
流畅度（FL）：CHRF分数衡量文本自然度
综合指标：J = STA×SIM×FL 的几何平均数

我是文本解毒任务的评估人员，现在需要评估文本解毒模型的性能。
</任务背景>

<任务介绍>
你将看到两句话。你的任务是判断它们是否表达相同（或相近）的意思。
句子不需要完全一致——其中一句可能带有冒犯性，而另一句可以用中性语气表达相同内容。

如果其中一句或两句包含无意义内容（非单词、无意义的词语组合等），请选择"无法判断"。
<\任务介绍>

<评价指标>
content_score:
> 1：含义相同（或相近）
> 0.5：无法判断：其中一句或两句包含无意义内容（非单词、无意义的词语组合等）
> 0：含义不同（或不相近）
<\评价指标>

<需要评价的数据> 
句子1：{toxic_sentence}

句子2：{neutral_sentence}
<\需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason" string
}
```
<\输出数据格式> 