<任务背景介绍> 
在自然语言处理领域，文本中常常包含一些具有攻击性、歧视性、虚假信息等有害内容，这些内容不仅会影响信息的传播和交流，还可能对用户造成不良影响。文本解毒任务旨在对含有有害信息的文本进行处理，去除或修正这些有害内容，使其成为积极、健康、真实且无歧视性的文本，从而提升文本质量和信息传递的有效性。
<\任务背景介绍>

<任务介绍>
你的任务是判断句子1和句子2背后表达中性意思是否可能相同（不要管这两个句子里骂人的部分，比如句子1里面用侮辱性词汇，句子2里用中性词汇表达了相同的内容，那也算是表达相同的意思）。
<\任务介绍>

<评价指标>
content_score：判断句子1和句子2表达意思是否相同，非常可能相同为5分，可能相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。
<\评价指标>

<你需要评价的数据> 
句子1：{toxic_sentence}

句子2：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason" string
}
```
<\输出数据格式> 