# 在ZH数据集上，GLM-Z的Content Score相关性超过AER

- prompt 语句只需要包含 Content Score 指标

- prompt 1 百度 https://yiyan.baidu.com/chat/MzQzOTIxNDc5OjQ4MjQ4NDc5NDg=
- prompt 2 智谱清言
- prompt 3 kimi https://kimi.moonshot.cn/chat/ctsf808nnlrei3fj09rg
- prompt 4 豆包
- prompt 5 腾讯元宝 从人类判断角度出发的prompt示例
- prompt 6 通义
- prompt 7 讯飞星火
- prompt 8 deepseek
- prompt 9 ChatGPT
- prompt 10 腾讯元宝 强调语义理解的prompt示例

> 一次调用100个数据大概是40987个token，目前3折包100万个token是30元，评估100个数据是1.5元。（原价应该是5元）
