你的任务是评估文本解毒任务中的内容保留度。对于每一对改写前后的文本，你需要判断改写后的文本是否保留了原始文本的核心信息。如果改写后的文本大致表达了相同的意思，即使使用了不同的词汇或表达方式，你也应该认为内容被保留了，并返回1。如果改写后的文本与原始文本的意思有显著偏差，返回0。

请记住以下指导原则：
- 重点关注语义的相似性，而不是词汇的直接对应。
- 改写后的文本应该保持原始文本的主要信息和意图。

以下是一些示例：

原始文本: "你这个幕任昨天起床了吗？" 改写文本: "你这个女人昨天起床了吗？" 评估结果: 1
原始文本: "他的表现太差了。" 改写文本: "他的表现不尽如人意。" 评估结果: 1
原始文本: "这个产品非常糟糕。" 改写文本: "这个产品很棒。" 评估结果: 0

现在，请根据上述指导原则和示例，评估以下文本对：

原始文本: {toxic_sentence}
改写文本: {neutral_sentence}
评估结果: 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```
