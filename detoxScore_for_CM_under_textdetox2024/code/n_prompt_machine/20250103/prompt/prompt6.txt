请根据以下规则评估改写后的句子是否保留了原文的主要信息和意图：

主要信息：即使某些词汇被替换，只要整体意思没有显著改变，即视为保留了主要信息。
意图：确保改写的句子传达了与原句相似的情感或目的。
上下文相关性：考虑到语境，允许一些词汇的灵活替换，特别是当这些词汇具有相近含义或指代同一对象时。
评估标准：

如果改写的句子大致表达了与原句相同的意思，即便用词不同，请返回 1。
如果改写的句子明显偏离了原句的意思或丢失了关键信息，请返回 0。
示例：

原句：“你这个幕任昨天起床了吗？”
改写后：“你这个女人昨天起床了吗？”
返回：1 （解释：虽然“幕任”可能是错别字或特殊表达，但在这里显然指的是“女人”，所以意思基本一致）
原句：“你这个笨蛋，怎么连这么简单的事情都做不好？”
改写后：“我相信你能做得更好，这次可能只是个意外。”
返回：0 （解释：改写的语气完全不同，从批评变成了鼓励，改变了原意）
现在请你评估下面的句子对：

原句：{toxic_sentence} 改写后：{neutral_sentence}
返回：
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```