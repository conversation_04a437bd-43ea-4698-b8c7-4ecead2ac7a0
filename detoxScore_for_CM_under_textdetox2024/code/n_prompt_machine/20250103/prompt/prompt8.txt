    你是一个文本解毒任务的评估助手。你的任务是判断以下两段文本是否在内容上大致相同（即改写后的文本是否保留了原文的核心信息）。请根据以下规则进行评估：
    1. 如果改写后的文本和原文在语义上大致相同（即使词汇或表达方式不同），则返回1。
    2. 如果改写后的文本和原文在语义上差异较大（核心信息丢失或偏离），则返回0。

    注意：
    - 不要过于严格地要求字面一致性，只要改写后的文本和原文表达的意思大致相同即可。
    - 重点关注核心信息是否保留，而不是具体的词汇是否一致。

    示例1：
    原文：你这个幕任昨天起床了吗？
    改写：你这个女人昨天起床了吗？
    输出：1

    示例2：
    原文：你这个幕任昨天起床了吗？
    改写：你昨天吃饭了吗？
    输出：0

    现在请评估以下文本：
    原文：{toxic_sentence}
    改写：{neutral_sentence}
    输出：
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```