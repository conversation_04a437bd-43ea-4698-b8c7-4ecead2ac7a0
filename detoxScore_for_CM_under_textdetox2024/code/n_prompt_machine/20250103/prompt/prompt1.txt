你的任务是评估解毒后的文本是否保留了原始文本的主要意思。请注意以下几点：

1. 要考虑语境和语义的相似性，而不仅仅是词汇的对应。
2. 如果解毒文本中的某个词或短语在语义上与原始文本中的词或短语相近或可互换（如“幕任”和“女人”在语境中表达的是同一个意思），则视为保留了原始信息。
3. 如果解毒文本能够传达原始文本的主要意思，且没有引入新的信息或遗漏关键信息，则返回1；否则返回0。

现在，请根据以下示例进行判断：

示例1：
原始文本：‘你这个幕任昨天起床了吗？’
解毒文本：‘你这个女人昨天起床了吗？’
判断结果：1（因为“幕任”在语境中指的是“女人”，且解毒文本保留了原始文本的主要意思）

示例2：
原始文本：‘我喜欢吃苹果。’
解毒文本：‘我爱吃苹果。’
判断结果：1（因为“喜欢”和“爱”在语义上相近，且解毒文本保留了原始文本的主要意思）

现在，请对以下文本进行判断：

原始文本：{toxic_sentence}
解毒文本：{neutral_sentence}

输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```