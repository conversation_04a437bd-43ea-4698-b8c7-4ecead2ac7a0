你的任务是评估修改后的文本是否保留了原意，即判断两个句子是否表达相同或相近的意思。请根据以下标准进行评分：
- 如果两个句子表达相同或非常接近的意思，返回1；
- 如果两个句子表达的意思有明显差异，返回0。

示例1:
原始文本: "你这个幕任昨天起床了吗？"
修改后文本: "你这个女人昨天起床了吗？"
评分: 1（因为两句话表达的意思非常接近）

示例2:
原始文本: "我草拟妈，我喜欢看电影。"
修改后文本: "我热爱观看电影。"
评分: 1（虽然用词不同，修改后的文本去掉了不恰当的言语，整体语境和情感色彩发生了变化，但意思非常接近）
示例3:
原始文本: "他跑得很快。"
修改后文本: "她跑得很慢。"
评分: 0（意思完全不同）

请注意，有些词语在不同的上下文中可能有不同的含义。请尽量从整体上理解句子的意思，而不仅仅是字面上的词语匹配。如果两个句子在大多数情况下可以互换使用且不会改变原意，那么它们应该被认为是表达相同或相近的意思。

现在，请根据上述指导原则和示例，评估以下文本对：

原始文本: {toxic_sentence}
修改后文本: {neutral_sentence}
评分: 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```