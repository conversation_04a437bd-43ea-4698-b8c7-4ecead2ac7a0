{"cells": [{"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["./results/prompt1.csv\n", "./results/prompt10.csv\n", "./results/prompt2.csv\n", "./results/prompt3.csv\n", "./results/prompt4.csv\n", "./results/prompt5.csv\n", "./results/prompt6.csv\n", "./results/prompt7.csv\n", "./results/prompt8.csv\n", "./results/prompt9.csv\n", "./results/promptg.csv\n", "| Prompt | Accuracy | Recall | Precision | F1 |\n", "| --- | --- | --- | --- | --- |\n", "| ./results/prompt1.csv | 0.6900 | 0.3791 | 0.4423 | 0.4083 |\n", "| ./results/prompt10.csv | 0.8000 | 0.4396 | 0.4494 | 0.4444 |\n", "| ./results/prompt2.csv | 0.7700 | 0.4231 | 0.4477 | 0.4350 |\n", "| ./results/prompt3.csv | 0.7600 | 0.4176 | 0.4471 | 0.4318 |\n", "| ./results/prompt4.csv | 0.9100 | 0.5501 | 0.7092 | 0.5671 |\n", "| ./results/prompt5.csv | 0.8300 | 0.4560 | 0.4511 | 0.4536 |\n", "| ./results/prompt6.csv | 0.7600 | 0.4176 | 0.4471 | 0.4318 |\n", "| ./results/prompt7.csv | 0.7800 | 0.4286 | 0.4483 | 0.4382 |\n", "| ./results/prompt8.csv | 0.7800 | 0.4286 | 0.4483 | 0.4382 |\n", "| ./results/prompt9.csv | 0.8200 | 0.4505 | 0.4505 | 0.4505 |\n", "| ./results/promptg.csv | 1.0000 | 1.0000 | 1.0000 | 1.0000 |\n"]}], "source": ["# 读取文件A和文件B，其中A文件为gloden标注文件，B文件为模型预测文件\n", "# 输出文件A列名为x的列和文件B列名为y的列的Accuracy、Recall、Precision、F1、Pearson、<PERSON><PERSON><PERSON>\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import accuracy_score, recall_score, precision_score, f1_score\n", "gloden_file = (\n", "    \"/Users/<USER>/DetoxScore/detoxScore_for_CM_under_textdetox2024/code/data/CM/ZH.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "column_name = \"content_score\"\n", "# column_name = \"toxic_pairwise_score\"\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "    \n", "    print(model_file_output)\n", "\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[column_name]\n", "    col_model = df_model[column_name]\n", "\n", "    col_gloden = [int(i * 100) for i in col_gloden]   \n", "    col_model = [int(i * 100) for i in col_model]   \n", "\n", "    # 计算各项指标\n", "    accuracy = accuracy_score(col_gloden, col_model)\n", "    recall = recall_score(col_gloden, col_model, average='macro')\n", "    precision = precision_score(col_gloden, col_model, average='macro')\n", "    f1 = f1_score(col_gloden, col_model, average='macro')\n", "\n", "    print_data.append([model_file_output, accuracy, recall, precision, f1])\n", "    \n", "# 输出为markdown格式\n", "print(\"| Prompt | Accuracy | Recall | Precision | F1 |\")\n", "print(\"| --- | --- | --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f | %.4f | %.4f |\" % (data[0], data[1], data[2], data[3], data[4]))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: './results2/'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 13\u001b[0m\n\u001b[1;32m      8\u001b[0m gloden_file \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m      9\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/Users/<USER>/DetoxScore/detoxScore_for_CM_under_textdetox2024/code/data/CM/ZH.tsv\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     10\u001b[0m )\n\u001b[1;32m     12\u001b[0m model_file_output_dir \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m./results2/\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m---> 13\u001b[0m model_file_outputs \u001b[38;5;241m=\u001b[39m [f \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m \u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlistdir\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel_file_output_dir\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mif\u001b[39;00m f\u001b[38;5;241m.\u001b[39mendswith(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.csv\u001b[39m\u001b[38;5;124m\"\u001b[39m)]\n\u001b[1;32m     14\u001b[0m model_file_outputs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28msorted\u001b[39m(model_file_outputs)\n\u001b[1;32m     16\u001b[0m print_data \u001b[38;5;241m=\u001b[39m []\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: './results2/'"]}], "source": ["# 读取文件A和文件B，其中A文件为gloden标注文件，B文件为模型预测文件\n", "# 输出文件A列名为x的列和文件B列名为y的列的Accuracy、Recall、Precision、F1、Pearson、<PERSON><PERSON><PERSON>\n", "import os\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import accuracy_score, recall_score, precision_score, f1_score\n", "from scipy.stats import pearsonr, spearmanr\n", "gloden_file = (\n", "    \"/Users/<USER>/DetoxScore/detoxScore_for_CM_under_textdetox2024/code/data/CM/ZH.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results2/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[\"fluency_score\"]\n", "    # 推算 col_model的fluency_score 的值，fluency_score = 1 if neutral_fluency >= toxic_fluency : 0，转换为整数\n", "    col_model = np.where(df_model[\"neutral_fluency\"] >= df_model[\"toxic_fluency\"], 1, 0)\n", "    col_gloden = [int(i * 100) for i in col_gloden]   \n", "    col_model = [int(i * 100) for i in col_model]\n", "\n", "    # 计算各项指标\n", "    accuracy = accuracy_score(col_gloden, col_model)\n", "    recall = recall_score(col_gloden, col_model, average='macro')\n", "    precision = precision_score(col_gloden, col_model, average='macro')\n", "    f1 = f1_score(col_gloden, col_model, average='macro')\n", "\n", "    print_data.append([model_file_output, accuracy, recall, precision, f1])\n", "    \n", "# 输出为markdown格式\n", "print(\"| Prompt | Accuracy | Recall | Precision | F1 |\")\n", "print(\"| --- | --- | --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f | %.4f | %.4f |\" % (data[0], data[1], data[2], data[3], data[4]))"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["| Prompt | <PERSON> | <PERSON><PERSON><PERSON> |\n", "| --- | --- | --- | --- | --- |\n", "| ./results/prompt1.csv | -0.1670 | -0.1670 |\n", "| ./results/prompt10.csv | -0.1106 | -0.1106 |\n", "| ./results/prompt2.csv | -0.1269 | -0.1269 |\n", "| ./results/prompt3.csv | -0.1321 | -0.1321 |\n", "| ./results/prompt4.csv | 0.3196 | 0.3196 |\n", "| ./results/prompt5.csv | -0.0927 | -0.0927 |\n", "| ./results/prompt6.csv | -0.1321 | -0.1321 |\n", "| ./results/prompt7.csv | -0.1216 | -0.1216 |\n", "| ./results/prompt8.csv | -0.1216 | -0.1216 |\n", "| ./results/prompt9.csv | -0.0989 | -0.0989 |\n", "| ./results/promptg.csv | 1.0000 | 1.0000 |\n"]}], "source": ["from scipy.stats import pearsonr, spearmanr\n", "# 输出文件A列名为x的列和文件B列名为y的列的Pearson、<PERSON><PERSON><PERSON>\n", "print_data = []\n", "gloden_file = (\n", "    \"/Users/<USER>/DetoxScore/detoxScore_for_CM_under_textdetox2024/code/data/CM/ZH.tsv\"\n", ")\n", "\n", "model_file_output_dir = \"./results/\"\n", "model_file_outputs = [f for f in os.listdir(model_file_output_dir) if f.endswith(\".csv\")]\n", "model_file_outputs = sorted(model_file_outputs)\n", "column_name = \"content_score\"\n", "# column_name = \"toxic_pairwise_score\"\n", "\n", "print_data = []\n", "\n", "for model_file_output in model_file_outputs:\n", "    model_file_output = model_file_output_dir + model_file_output\n", "    # 读取文件\n", "    df_gloden = pd.read_csv(gloden_file, sep='\\t')\n", "    df_model = pd.read_csv(model_file_output)\n", "    # 提取需要的列\n", "    col_gloden = df_gloden[column_name]\n", "    col_model = df_model[column_name]\n", "\n", "    # 计算各项指标\n", "    pearson = pearsonr(col_gloden, col_model)[0]\n", "    spearman = spearmanr(col_gloden, col_model)[0]\n", "    print_data.append([model_file_output, pearson, spearman])\n", "\n", "# 输出为markdown格式\n", "print(\"| Prompt | Pearson | Spearman |\")\n", "print(\"| --- | --- | --- | --- | --- |\")\n", "for data in print_data:\n", "    print(\"| %s | %.4f | %.4f |\" % (data[0], data[1], data[2]))"]}], "metadata": {"kernelspec": {"display_name": "fight4human", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}