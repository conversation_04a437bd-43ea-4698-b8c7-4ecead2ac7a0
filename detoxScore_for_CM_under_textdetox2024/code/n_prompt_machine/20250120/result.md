
# 在ZH数据集上，GLM-F的Content Score相关性超过AER

## prompt 1
```
您将看到两个句子。您的任务是指出它们的意思是否相同（或接近）。
这两个句子不必完全相同，其中一个句子可以具有攻击性，而另一个句子则是用中性的语气表达同样的意思。

配对示例
句子1：尽管浪，谁接盘谁是狗，不得house 
句子2：尽管浪，谁接盘谁是狗，不得好下场

句子1：重点是视频里还不是同一个默囧 
句子2：重点是视频里还不是同一个黑人囧

句子1：你敢说你不是打拳的？
句子2：你敢说你不是极端女权主义？

非配对的示例
句子1：歧视，歧视，还是歧视??
句子2：反对歧视，反对歧视，还是反对歧视？？

句子1：黑命贵祸害原住民是事实
句子2：伤害原住民是残忍的。

句子1：又不喜欢打拳来到国内
句子2：我不喜欢极端女权主义来到国内

接下来正式开始你的任务，请看两个句子：
句子1：{toxic_sentence}
句子2：{neutral_sentence}

在不考虑语言色彩的情况下，句子1和句子2表达意思相同吗？

非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。

输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```

```

# prompt2
调整了一下

# prompt2-plus
换成了chatGLM-4-plus

## result
| Prompt | Pearson | Spearman |
| --- | --- | --- |
| prompt1.csv | 0.0449 | 0.0449 |
| prompt2.csv | 0.0300 | 0.0300 |
| prompt2-plus.csv | 0.1367 | 0.1367 |

## 人类标注数据：
0: 9
1: 91
## prompt1
0: 27
1: 73
## prompt2
0: 29
1: 71
## prompt2-plus
0: 17
1: 83