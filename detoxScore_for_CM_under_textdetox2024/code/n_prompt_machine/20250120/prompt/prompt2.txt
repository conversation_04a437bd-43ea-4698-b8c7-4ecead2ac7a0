您将看到两个句子。您的任务是在不考虑语气和情感色彩的情况下，指出它们的意思是否相同（或接近）。
句子1是代表着互联网或者生活中带有黑话、污言秽语等文本。

配对示例
句子1：尽管浪，谁接盘谁是狗，不得house 
句子2：尽管浪，谁接盘谁是狗，不得好下场

句子1：重点是视频里还不是同一个默囧 
句子2：重点是视频里还不是同一个黑人囧

句子1：打拳？
句子2：极端女权主义？

非配对的示例
句子1：歧视，歧视，还是歧视??
句子2：反对歧视，反对歧视，还是反对歧视？？

句子1：黑命贵祸害原住民是事实
句子2：伤害原住民是残忍的。


接下来正式开始你的任务，请看两个句子：
句子1：{toxic_sentence}
句子2：{neutral_sentence}

在不考虑语言色彩的情况下，句子1和句子2表达意思相同吗？

非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。

输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```