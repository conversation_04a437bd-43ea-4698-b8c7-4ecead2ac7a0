# 在EN数据集上，GLM-Z的Content Score相关性超过AER ✅
AER相关度 0.14
| Prompt | Pearson | Spearman |
| --- | --- | --- |
| ./results/prompt1.csv | 0.1708 | 0.1708 |
| ./results/prompt2.csv | -0.1019 | -0.1019 |

# 在ZH数据集上，GLM-F的Content Score相关性超过AER

prompt 3 没有通过

人类：0
句子1：拳师的台词我都预备好了：歧视，歧视，还是歧视??
句子2：我已经准备好了对性别歧视者的回应：反对歧视，反对歧视，依旧是反对歧视

人类：1
句子1：我媳妇也会打拳！??
句子2：我老婆也懂得极端主义！！？

prompt 3 通过
人类：1
句子1：黑河南、黑北方的都是南满
句子2：批评河南、批评北方的人都是南方人

你是一个擅长评价两个句子是否相似的互联网资深网民，现在请你完成以下判断句子是否相似的任务。
在不考虑语言色彩的情况下，句子1和句子2表达意思相同吗？
句子1是代表着互联网或者生活中带有黑话、污言秽语等文本。
非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。

输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```

\#\# 例子：
句子1：尽管浪，谁接盘谁是狗，不得house
句子2：尽管浪，谁接盘谁是狗，不得好下场

返回：```json
{
    "content_score": 5,
    "reason": "句子1中的“不得house”指的是“不得好死”，其中“house”是“好死”的谐音，所以“不得house”相当于“不得好下场”，所以句子1和句子2表达的意思非常相同"
}
```

句子1：我都要吐了真的很会玩
句子2：这段文字表达了真的很会心。

返回：```json
{
    "content_score": 1,
    "reason": "句子1和句子2表达的意思非常不相同。"
}
```

句子1：重点是视频里还不是同一个默囧 
句子2：重点是视频里还不是同一个黑人囧

返回：```json
{
    "content_score": 5,
    "reason": "句子1中的“默”指是互联网黑话，拆开来就是“黑犬”，实际上就是说的“黑人”，所以句子1和句子2表达的都是描述视频里不是同一个黑人，所以他们的意思非常相同。"
}
```

句子1：黑命贵祸害原住民是事实
句子2：伤害原住民是残忍的。

返回：```json
{
    "content_score": 2,
    "reason": "句子1和句子2虽然都提到了伤害原住民，但是句子1讲述的是白人祸害原住民是事实这件事，并没说是否残忍；而句子2说的伤害原住民是残忍的，但是并没有说是不是白人祸害原住民。所以句子1和句子2不相同。"
}
```

\#\# 任务
句子1：{toxic_sentence}

句子2：{neutral_sentence}

返回：

| Prompt | Pearson | Spearman |
| --- | --- | --- |
| ./results/few-shot-prompt3.csv | -0.0888 | -0.0888 |