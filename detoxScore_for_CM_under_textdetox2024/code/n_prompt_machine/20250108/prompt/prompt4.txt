你现在的角色是一个语义相关性判定标注者。
给你的是两个句子，第一个句子是含有有毒文字的句子，第二个句子是将第一个句子的有毒文字转化后变成的无毒句子。你的任务是判断第二个句子移除有毒文字以后，是否和第一个句子表达的是同一个语义。非常相同为5分，相同为4分，无法判断为3分，不相同为2分，非常不相同为1分。
有毒文字的定义是：有毒文字指那些明显包含粗俗和粗鲁词汇的文本，其中仍存在有意义的中性内容，而不包括那些隐含的、如讽刺或被动攻击性等无法找到中性内容的文本类型.

请你对下面的两句文本进行判定，给出分数。
第一个句子：拳师的台词我都预备好了：歧视，歧视，还是歧视??
第二个句子：我已经准备好了对性别歧视者的回应：反对歧视，反对歧视，依旧是反对歧视

输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "content_score": number,
    "reason": "string"
}
```