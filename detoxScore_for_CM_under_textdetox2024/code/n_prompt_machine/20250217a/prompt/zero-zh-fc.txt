<任务介绍>
你的任务是评价句子是否流畅，是否符合语法规范，是否能让你理解其意思。
<\任务介绍>

<评价指标>
neutral_fluency：句子是否流畅，是否符合语法规范，是否能让你理解其意思。
    <评价指标-neutral_fluency=1的情况>
    满足下列条件之一：
        （1）句子写得完全正确，用词正确且恰当；
        （2）只有标点符号错误或大小写错误；
        （3）句子只有有轻微错误，不影响你理解其意思。
    </评价指标-neutral_fluency=1的情况>
    <评价指标-neutral_fluency=0.5的情况>
    满足下列条件之一：
        （1）句子有很多错误，但不影响理解句子意思
        （2）文中有拼写错误--单词、赐予的形式不恰当、粘连在一起、拼写错误--但仍能完全理解文章的意思。
    </评价指标-neutral_fluency=0.5的情况>
    <评价指标-neutral_fluency=0>
    满足下列条件之一：
        （1）句子的语法错误使您无法理解其含义
        （2）文字歪曲、使用完全不恰当，还有一些奇怪的符号，使得让你无法理解全文的意思
    </评价指标-neutral_fluency=0.5的情况>
<\评价指标>

<你需要评价的数据> 
句子：{neutral_sentence}
<\你需要评价的数据>

<输出数据格式> 
输出应该是按照以下模式格式化的 markdown 代码片段，包括前面和后面的“```json ”和“````”：

```json
{
    "neutral_fluency": number,
    "reason": "string"
}
```
<\输出数据格式> 