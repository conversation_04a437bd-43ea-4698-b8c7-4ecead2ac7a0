{"glm": {"apiKey": "468ebeaf0a61656a71403154eddc36dd.GjIh1az6tWmgrBGo", "zh_kimi_prompt_file_id": "1735032660_32ca46732f43471cbc549f16b3060cbc", "baseUrl": "https://open.bigmodel.cn/api/paas/v4/", "model": "glm-4.5", "temperature": 0.1}, "gemini": {"apiKey": "sk-mkyiKhpHka6uG4XOWjOvPBgv5zFtS1QMFl3rmsGKeghIOrj6", "baseUrl": "https://openai.weavex.tech/v1", "model": "gemini-2.5-pro", "temperature": 0.1}, "deepseek": {"apiKey": "***********************************", "baseUrl": "https://api.deepseek.com/v1", "model": "deepseek-reasoner", "temperature": 0.1}, "openai": {"apiKey": "sk-mkyiKhpHka6uG4XOWjOvPBgv5zFtS1QMFl3rmsGKeghIOrj6", "baseUrl": "https://openai.weavex.tech/v1", "model": "o3", "temperature": 0.1}}