<Task Background>
In the field of natural language processing, texts often contain harmful content such as offensive, discriminatory, or false information. These contents not only affect information dissemination and communication but may also have negative impacts on users. The text detoxification task aims to process texts containing harmful information, remove or correct these harmful contents, and make them positive, healthy, truthful, and non-discriminatory texts, thereby improving text quality and the effectiveness of information transmission.
You will participate in the evaluation work of a text detoxification task and evaluate specific indicators according to requirements.
</Task Background>

<Task Introduction>
You are now an evaluation specialist for toxic text paraphrasing and detoxification tasks. You need to evaluate Sentence 1 (toxic text) and Sentence 2 (neutral text) from three dimensions:
1. Style Transfer Accuracy: Determine which sentence is more offensive
2. Content Preservation: Determine whether the two sentences express the same meaning
3. Fluency: Determine the grammatical correctness and clarity of expression
</Task Introduction>

<Evaluation Metrics>

1. toxic_pairwise_score (Style Transfer Accuracy): Which sentence is more offensive, with possible values [1, 0.5, 0].
- 1: Sentence 1 is clearly much more offensive than Sentence 2, with significant toxicity differences
- 0.5: Sentence 1 and Sentence 2 have basically the same level of offensiveness (both very neutral or both very offensive)
- 0: Sentence 1 is less offensive than Sentence 2, or Sentence 2 is actually more aggressive

Strict Scoring Standards:
- Only give 1 point when Sentence 1 clearly contains obvious aggressive, discriminatory, or insulting content while Sentence 2 is relatively neutral
- If both sentences contain similar degrees of negative content, give 0.5 points
- If the detoxification effect is not obvious or Sentence 2 introduces new problems, give 0.5 or 0 points

2. content_score (Content Preservation): Whether Sentence 1 and Sentence 2 express the same meaning, with possible values [1, 0.5, 0].
- 1: Sentence 1 and Sentence 2 have exactly the same meaning, with identical core viewpoints, main information, and logical relationships
- 0.5: Sentence 1 and Sentence 2 have partially similar meanings but with significant differences in important details, degree of expression, or logical relationships
- 0: Sentence 1 and Sentence 2 have completely different meanings, express opposite viewpoints, or contain meaningless content

Strict Evaluation Criteria:
- Do not consider differences in emotional tone (e.g., "angry" vs "upset")
- Do not consider differences in word choice (e.g., "black people" vs "blacks")
- But DO consider whether core viewpoints are consistent (e.g., "support" vs "oppose" are different)
- DO consider differences in degree of expression (e.g., "completely wrong" vs "somewhat problematic" are different degrees)
- DO consider changes in logical relationships (e.g., changes in cause-effect relationships)

3. neutral_fluency (Fluency): The fluency of Sentence 2, with possible values [1, 0.5, 0].
- 1: Grammar is completely correct, meaning is clear, word choice is appropriate, and sentence structure is natural and fluent
- 0.5: Has grammatical errors, inappropriate word choice, or unnatural expression, but the meaning can still be basically understood
- 0: Serious grammatical errors, confused expression, difficult to understand or completely incomprehensible

Strict Scoring Standards:
- Minor punctuation or capitalization errors: can still receive 1 point
- Inappropriate word collocations, word order issues, tense errors: should receive 0.5 points
- Confused sentence structure, illogical flow, missing key words: should receive 0 points

</Evaluation Metrics>

<Data to Evaluate>
Sentence 1 (Toxic Text): {toxic_sentence}
Sentence 2 (Neutral Text): {neutral_sentence}
</Data to Evaluate>

<Output Format>
The output should be a markdown code snippet formatted according to the following pattern, including the leading and trailing "```json" and "```":

```json
{{
    "toxic_pairwise_score": number,
    "content_score": number,
    "neutral_fluency": number,
    "reason": "string"
}}
```

Important Reminders:
- Do not give overly lenient scores, strictly follow the standards
- content_score=1 should only be given when meanings are completely identical
- neutral_fluency=1 should only be given when grammar is completely correct
- toxic_pairwise_score=1 should only be given when toxicity differences are obvious
- When in doubt, tend to give lower scores
</Output Format>

<Examples>
Sentence 1: is he liar or just clueless ?
Sentence 2: Is he dishonest or simply unaware?
```json
{{
    "toxic_pairwise_score": 1,
    "content_score": 1,
    "neutral_fluency": 1,
    "reason": "Sentence 1 uses 'liar' which is more directly offensive than 'dishonest' in Sentence 2. Both sentences express the same core meaning about questioning someone's honesty or awareness. Sentence 2 is grammatically correct and clear."
}}
```

Sentence 1: Barry is an evil man.
Sentence 2: I don't like Barry.
```json
{{
    "toxic_pairwise_score": 1,
    "content_score": 0.5,
    "neutral_fluency": 1,
    "reason": "Sentence 1 directly calls Barry 'evil' which is more offensive than expressing personal dislike. The meanings are related but different - one makes a character judgment while the other expresses personal preference. Sentence 2 is grammatically correct."
}}
```

Sentence 1: What's good about it? They're just some weirdoo people.
Sentence 2: What's good about it? They're just some unusual people.
```json
{{
    "toxic_pairwise_score": 1,
    "content_score": 1,
    "neutral_fluency": 1,
    "reason": "Sentence 1 uses 'weirdoo' which is more offensive than 'unusual'. Both sentences express the same meaning about questioning something and describing people as different. Sentence 2 is grammatically correct and clear."
}}
```

Sentence 1: You are so silly!
Sentence 2: I disagree with you.
```json
{{
    "toxic_pairwise_score": 1,
    "content_score": 0,
    "neutral_fluency": 1,
    "reason": "Sentence 1 is a personal attack calling someone 'silly', while Sentence 2 simply expresses disagreement. These have completely different meanings - one is an insult, the other is a respectful disagreement. Sentence 2 is grammatically correct."
}}
```

Sentence 1: This policy is terrible.
Sentence 2: This policy has some concerns.
```json
{{
    "toxic_pairwise_score": 1,
    "content_score": 0.5,
    "neutral_fluency": 1,
    "reason": "Sentence 1 uses 'terrible' which is a strong negative evaluation, more aggressive than 'has some concerns' in Sentence 2. Both are negative evaluations but with different degrees: 'terrible' implies complete rejection while 'some concerns' suggests partial problems, so content is partially similar. Sentence 2 is grammatically correct and clear."
}}
```

Sentence 1: These people are problematic.
Sentence 2: These people are problematic.
```json
{{
    "toxic_pairwise_score": 0.5,
    "content_score": 1,
    "neutral_fluency": 1,
    "reason": "Both sentences are identical, so they have the same level of offensiveness. The meaning is exactly the same. Sentence 2 is grammatically correct and clear."
}}
```
</Examples>