#!/usr/bin/env python3
"""
快速相关性计算脚本
基于您原有的代码逻辑，但更加通用和便捷
"""

import os
import pandas as pd
import numpy as np
from scipy.stats import pearsonr, spearmanr


def auto_correlation_analysis(column_name="j_score"):
    """
    自动相关性分析 - 自动识别当前目录下的CSV文件并计算相关性
    """
    print("🔍 开始自动相关性分析...")
    print(f"当前目录: {os.getcwd()}")
    print(f"比较列名: {column_name}")

    # 获取当前目录下的所有CSV文件，排除备份文件
    current_dir = "."
    model_files = [f for f in os.listdir(current_dir) if f.endswith(".csv") and "_backup.csv" not in f]
    model_files = sorted(model_files)

    if not model_files:
        print(f"❌ 当前目录中未找到任何CSV文件")
        return

    print(f"📁 找到 {len(model_files)} 个CSV文件")
    print("=" * 60)

    # 分组处理英文和中文文件
    en_files = [f for f in model_files if "_en" in f.lower()]
    zh_files = [f for f in model_files if "_zh" in f.lower()]
    other_files = [f for f in model_files if "_en" not in f.lower() and "_zh" not in f.lower()]

    all_results = []

    # 处理英文文件
    if en_files:
        print(f"📝 处理 {len(en_files)} 个英文文件...")
        # 尝试多个可能的黄金标准文件路径
        en_golden_paths = [
            "../../../TDE/EN.tsv",  # 从 code/openai/ 到根目录
            "../../TDE/EN.tsv",     # 从 code/ 到根目录
            "../TDE/EN.tsv",        # 从当前目录的上级
            "TDE/EN.tsv",           # 当前目录下
            "EN.tsv"                # 当前目录下
        ]
        en_golden_file = find_existing_file(en_golden_paths)
        if en_golden_file:
            en_results = process_files_with_golden(en_files, current_dir, en_golden_file, column_name, "EN")
            all_results.extend(en_results)
        else:
            print(f"  ❌ 未找到英文黄金标准文件，尝试过的路径: {en_golden_paths}")

    # 处理中文文件
    if zh_files:
        print(f"📝 处理 {len(zh_files)} 个中文文件...")
        # 尝试多个可能的黄金标准文件路径
        zh_golden_paths = [
            "../../../TDE/ZH.tsv",  # 从 code/openai/ 到根目录
            "../../TDE/ZH.tsv",     # 从 code/ 到根目录
            "../TDE/ZH.tsv",        # 从当前目录的上级
            "TDE/ZH.tsv",           # 当前目录下
            "ZH.tsv"                # 当前目录下
        ]
        zh_golden_file = find_existing_file(zh_golden_paths)
        if zh_golden_file:
            zh_results = process_files_with_golden(zh_files, current_dir, zh_golden_file, column_name, "ZH")
            all_results.extend(zh_results)
        else:
            print(f"  ❌ 未找到中文黄金标准文件，尝试过的路径: {zh_golden_paths}")

    # 处理其他文件（无法自动识别语言的）
    if other_files:
        print(f"📝 发现 {len(other_files)} 个无法自动识别语言的文件:")
        for f in other_files:
            print(f"  - {f}")
        print("  💡 提示: 如需处理这些文件，请在文件名中包含 '_en' 或 '_zh' 标识")

    if not all_results:
        print("❌ 没有成功处理任何文件")
        print("💡 请确保:")
        print("   1. CSV文件名包含 '_en' 或 '_zh' 语言标识")
        print("   2. 黄金标准文件 EN.tsv 或 ZH.tsv 在可访问的路径中")
        return

    # 按照 Pearson 系数降序排列
    all_results.sort(key=lambda x: x[1], reverse=True)

    print("\n" + "=" * 60)
    print("📊 相关性分析结果 (按 Pearson 系数降序排列):")
    print("=" * 60)

    # 输出为markdown格式
    print("| 文件名 | 语言 | Pearson | Spearman |")
    print("| --- | --- | --- | --- |")
    for data in all_results:
        print("| %s | %s | %.4f | %.4f |" % (data[0], data[3], data[1], data[2]))

    # 分别统计英文和中文的结果
    en_results_only = [r for r in all_results if r[3] == "EN"]
    zh_results_only = [r for r in all_results if r[3] == "ZH"]

    print(f"\n📈 统计摘要:")
    print(f"   - 总处理文件数: {len(all_results)}")

    if en_results_only:
        print(f"   - 英文文件数: {len(en_results_only)}")
        print(f"   - 英文最高 Pearson: {max(en_results_only, key=lambda x: x[1])[1]:.4f} ({max(en_results_only, key=lambda x: x[1])[0]})")
        print(f"   - 英文平均 Pearson: {np.mean([x[1] for x in en_results_only]):.4f}")

    if zh_results_only:
        print(f"   - 中文文件数: {len(zh_results_only)}")
        print(f"   - 中文最高 Pearson: {max(zh_results_only, key=lambda x: x[1])[1]:.4f} ({max(zh_results_only, key=lambda x: x[1])[0]})")
        print(f"   - 中文平均 Pearson: {np.mean([x[1] for x in zh_results_only]):.4f}")


def find_existing_file(file_paths):
    """
    从路径列表中找到第一个存在的文件
    """
    for path in file_paths:
        if os.path.exists(path):
            return path
    return None


def smart_correlation_analysis(model_dir, column_name="j_score"):
    """
    智能相关性分析 - 自动根据文件名选择对应的黄金标准文件

    Args:
        model_dir: 模型输出目录
        column_name: 比较的列名
    """
    print(f"🔍 开始智能相关性分析...")
    print(f"模型输出目录: {model_dir}")
    print(f"比较列名: {column_name}")

    # 检查目录
    if not os.path.exists(model_dir):
        print(f"❌ 模型输出目录不存在: {model_dir}")
        return

    # 获取所有CSV文件，排除备份文件
    model_files = [f for f in os.listdir(model_dir) if f.endswith(".csv") and "_backup.csv" not in f]
    model_files = sorted(model_files)

    if not model_files:
        print(f"❌ 在目录 {model_dir} 中未找到任何CSV文件")
        return

    print(f"📁 找到 {len(model_files)} 个模型输出文件")
    print("=" * 60)

    # 分组处理英文和中文文件
    en_files = [f for f in model_files if "_en" in f.lower()]
    zh_files = [f for f in model_files if "_zh" in f.lower()]

    all_results = []

    # 处理英文文件
    if en_files:
        print(f"📝 处理 {len(en_files)} 个英文文件...")
        en_golden_file = "../../TDE/EN.tsv"
        en_results = process_files_with_golden(en_files, model_dir, en_golden_file, column_name, "EN")
        all_results.extend(en_results)

    # 处理中文文件
    if zh_files:
        print(f"📝 处理 {len(zh_files)} 个中文文件...")
        zh_golden_file = "../../TDE/ZH.tsv"
        zh_results = process_files_with_golden(zh_files, model_dir, zh_golden_file, column_name, "ZH")
        all_results.extend(zh_results)

    if not all_results:
        print("❌ 没有成功处理任何文件")
        return

    # 按照 Pearson 系数降序排列
    all_results.sort(key=lambda x: x[1], reverse=True)

    print("\n" + "=" * 60)
    print("📊 相关性分析结果 (按 Pearson 系数降序排列):")
    print("=" * 60)

    # 输出为markdown格式
    print("| 文件名 | 语言 | Pearson | Spearman |")
    print("| --- | --- | --- | --- |")
    for data in all_results:
        print("| %s | %s | %.4f | %.4f |" % (data[0], data[3], data[1], data[2]))

    # 分别统计英文和中文的结果
    en_results_only = [r for r in all_results if r[3] == "EN"]
    zh_results_only = [r for r in all_results if r[3] == "ZH"]

    print(f"\n📈 统计摘要:")
    print(f"   - 总处理文件数: {len(all_results)}")

    if en_results_only:
        print(f"   - 英文文件数: {len(en_results_only)}")
        print(f"   - 英文最高 Pearson: {max(en_results_only, key=lambda x: x[1])[1]:.4f} ({max(en_results_only, key=lambda x: x[1])[0]})")
        print(f"   - 英文平均 Pearson: {np.mean([x[1] for x in en_results_only]):.4f}")

    if zh_results_only:
        print(f"   - 中文文件数: {len(zh_results_only)}")
        print(f"   - 中文最高 Pearson: {max(zh_results_only, key=lambda x: x[1])[1]:.4f} ({max(zh_results_only, key=lambda x: x[1])[0]})")
        print(f"   - 中文平均 Pearson: {np.mean([x[1] for x in zh_results_only]):.4f}")


def process_files_with_golden(model_files, model_dir, golden_file, column_name, language):
    """
    使用指定的黄金标准文件处理模型文件列表
    """
    print(f"使用黄金标准文件: {golden_file}")

    # 检查黄金标准文件
    if not os.path.exists(golden_file):
        print(f"  ❌ 黄金标准文件不存在: {golden_file}")
        return []

    results = []

    for model_file in model_files:
        print(f"处理文件: {model_file}")
        model_file_path = os.path.join(model_dir, model_file)

        try:
            # 读取文件
            if golden_file.endswith('.tsv'):
                df_golden = pd.read_csv(golden_file, sep='\t')
            else:
                df_golden = pd.read_csv(golden_file)
            df_model = pd.read_csv(model_file_path)

            # 检查列是否存在
            if column_name not in df_golden.columns:
                print(f"  ❌ 黄金标准文件中未找到列 '{column_name}'")
                continue

            if column_name not in df_model.columns:
                print(f"  ❌ 模型文件中未找到列 '{column_name}'")
                continue

            # 提取需要的列
            col_golden = df_golden[column_name]
            col_model = df_model[column_name]

            # 检查数据长度
            if len(col_golden) != len(col_model):
                print(f"  ⚠️  数据长度不匹配: 黄金标准={len(col_golden)}, 模型={len(col_model)}")
                min_len = min(len(col_golden), len(col_model))
                col_golden = col_golden[:min_len]
                col_model = col_model[:min_len]

            # 计算各项指标
            pearson = pearsonr(col_golden, col_model)[0]
            spearman = spearmanr(col_golden, col_model)[0]

            results.append([model_file, pearson, spearman, language])
            print(f"  ✅ Pearson: {pearson:.4f}, Spearman: {spearman:.4f}")

        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            continue

    return results


def custom_correlation_analysis(golden_file, model_dir, column_name="j_score"):
    """
    自定义相关性分析

    Args:
        golden_file: 黄金标准文件路径
        model_dir: 模型输出目录
        column_name: 比较的列名
    """
    print(f"🔍 开始自定义相关性分析...")
    print(f"黄金标准文件: {golden_file}")
    print(f"模型输出目录: {model_dir}")
    print(f"比较列名: {column_name}")

    # 检查文件和目录
    if not os.path.exists(golden_file):
        print(f"❌ 黄金标准文件不存在: {golden_file}")
        return

    if not os.path.exists(model_dir):
        print(f"❌ 模型输出目录不存在: {model_dir}")
        return

    # 获取所有CSV文件，排除备份文件
    model_files = [f for f in os.listdir(model_dir) if f.endswith(".csv") and "_backup.csv" not in f]
    model_files = sorted(model_files)

    if not model_files:
        print(f"❌ 在目录 {model_dir} 中未找到任何CSV文件")
        return

    print(f"📁 找到 {len(model_files)} 个模型输出文件")
    print("=" * 60)

    results = process_files_with_golden(model_files, model_dir, golden_file, column_name, "CUSTOM")

    if not results:
        print("❌ 没有成功处理任何文件")
        return

    # 按照 Pearson 系数降序排列
    results.sort(key=lambda x: x[1], reverse=True)

    print("\n" + "=" * 60)
    print("📊 相关性分析结果 (按 Pearson 系数降序排列):")
    print("=" * 60)

    # 输出为markdown格式
    print("| Prompt | Pearson | Spearman |")
    print("| --- | --- | --- |")
    for data in results:
        print("| %s | %.4f | %.4f |" % (data[0], data[1], data[2]))


if __name__ == "__main__":
    import sys

    if len(sys.argv) == 1:
        # 自动分析当前目录
        auto_correlation_analysis()
    elif len(sys.argv) == 2:
        # 智能分析：只指定模型目录，自动选择对应的黄金标准文件
        model_dir = sys.argv[1]
        smart_correlation_analysis(model_dir)
    elif len(sys.argv) == 3:
        # 可能是智能分析+列名，或者是自定义黄金标准文件+模型目录
        if sys.argv[2] in ["j_score", "STA", "CS", "FS"]:
            # 智能分析+列名
            model_dir = sys.argv[1]
            column_name = sys.argv[2]
            smart_correlation_analysis(model_dir, column_name)
        else:
            # 自定义黄金标准文件和模型目录
            golden_file = sys.argv[1]
            model_dir = sys.argv[2]
            custom_correlation_analysis(golden_file, model_dir)
    elif len(sys.argv) == 4:
        # 自定义黄金标准文件、模型目录和列名
        golden_file = sys.argv[1]
        model_dir = sys.argv[2]
        column_name = sys.argv[3]
        custom_correlation_analysis(golden_file, model_dir, column_name)
    else:
        print("使用方法:")
        print("  python quick_correlation.py                           # 自动分析当前目录下的CSV文件")
        print("  python quick_correlation.py <model_dir>               # 智能分析指定目录")
        print("  python quick_correlation.py <model_dir> <column_name> # 智能分析+指定列名")
        print("  python quick_correlation.py <golden_file> <model_dir> # 自定义文件和目录")
        print("  python quick_correlation.py <golden_file> <model_dir> <column_name> # 完全自定义")
        print("")
        print("自动分析说明:")
        print("  - 自动扫描当前目录下的所有CSV文件")
        print("  - 包含'_en'的文件将使用 EN.tsv 作为黄金标准")
        print("  - 包含'_zh'的文件将使用 ZH.tsv 作为黄金标准")
        print("  - 自动搜索多个可能的黄金标准文件路径")
        print("  - 只需将脚本复制到目标目录即可使用")
