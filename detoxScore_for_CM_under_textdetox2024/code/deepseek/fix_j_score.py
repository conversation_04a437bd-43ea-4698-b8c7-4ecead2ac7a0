#!/usr/bin/env python3
"""
修复结果文件中的j_score计算错误
将原来的平均值计算 (sta + cs + fs) / 3 改为相乘计算 sta * cs * fs
"""

import pandas as pd
import os
import glob


def calculate_joint_score_corrected(sta, cs, fs):
    """
    正确的联合分数计算方式：三项相乘
    """
    return sta * cs * fs


def fix_j_score_in_file(file_path):
    """
    修复单个文件中的j_score值
    """
    print(f"正在处理文件: {file_path}")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        
        # 检查必要的列是否存在
        required_columns = ['STA', 'CS', 'FS', 'j_score']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"  ❌ 文件缺少必要的列: {missing_columns}")
            return False
        
        # 备份原始文件
        backup_path = file_path.replace('.csv', '_backup.csv')
        df.to_csv(backup_path, index=False)
        print(f"  💾 已创建备份文件: {backup_path}")
        
        # 计算修复前后的统计信息
        original_j_scores = df['j_score'].copy()
        
        # 重新计算j_score
        df['j_score'] = df.apply(lambda row: calculate_joint_score_corrected(row['STA'], row['CS'], row['FS']), axis=1)
        
        # 计算变化统计
        changed_count = (original_j_scores != df['j_score']).sum()
        total_count = len(df)
        
        print(f"  📊 处理结果:")
        print(f"     - 总记录数: {total_count}")
        print(f"     - 发生变化的记录数: {changed_count}")
        print(f"     - 原始j_score平均值: {original_j_scores.mean():.4f}")
        print(f"     - 修复后j_score平均值: {df['j_score'].mean():.4f}")
        
        # 保存修复后的文件
        df.to_csv(file_path, index=False)
        print(f"  ✅ 文件修复完成")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 处理文件时出错: {e}")
        return False


def main():
    """
    主函数：查找并修复所有结果文件
    """
    print("🔧 开始修复j_score计算错误...")
    print("=" * 60)
    
    # 获取当前脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 查找所有结果CSV文件
    csv_pattern = "etd_*_results_*.csv"
    csv_files = glob.glob(os.path.join(script_dir, csv_pattern))

    # 过滤掉备份文件并去重
    csv_files = list(set([f for f in csv_files if '_backup.csv' not in f]))
    
    if not csv_files:
        print("❌ 未找到任何结果文件")
        return
    
    print(f"📁 找到 {len(csv_files)} 个结果文件:")
    for file_path in csv_files:
        print(f"   - {os.path.basename(file_path)}")
    
    print("\n" + "=" * 60)
    
    # 询问用户确认
    response = input("是否继续修复这些文件？(y/N): ").strip().lower()
    if response not in ['y', 'yes', '是']:
        print("❌ 用户取消操作")
        return
    
    print("\n🚀 开始修复文件...")
    print("=" * 60)
    
    # 处理每个文件
    success_count = 0
    for file_path in csv_files:
        if fix_j_score_in_file(file_path):
            success_count += 1
        print()  # 空行分隔
    
    # 总结
    print("=" * 60)
    print(f"📊 修复完成总结:")
    print(f"   - 总文件数: {len(csv_files)}")
    print(f"   - 成功修复: {success_count}")
    print(f"   - 失败数量: {len(csv_files) - success_count}")
    
    if success_count == len(csv_files):
        print("🎉 所有文件修复成功！")
    else:
        print("⚠️  部分文件修复失败，请检查错误信息")
    
    print("\n💡 提示:")
    print("   - 原始文件已备份为 *_backup.csv")
    print("   - 如需恢复，可以将备份文件重命名回原文件名")


if __name__ == "__main__":
    main()
