# 少样本
from zhipuai import ZhipuAI
import re
import json
import pandas as pd

df_c = pd.read_csv(
    "./data/CM/ZH.tsv",
    sep="\t",
)

with open("config.json", "r") as f:
    config = json.load(f)

glmConfig = config.get("glm")


def extract_json_from_string(input_string):
    print(input_string)
    # 使用正则表达式匹配 JSON 部分
    json_match = re.search(r"\{.*\}", input_string, re.DOTALL)
    if json_match:
        json_str = json_match.group(0)
        # 将 JSON 字符串中的双引号替换为单引号
        json_str = json_str.replace('""', '"')
        # 将 JSON 字符串转换为字典
        json_dict = json.loads(json_str)
        return json_dict
    else:
        raise ValueError("未找到有效的 JSON 内容")


client = ZhipuAI(api_key=glmConfig["apiKey"])  # 填写您自己的APIKey

# 文件内容抽取
zh_kimi_prompt_file_content = client.files.content(
    file_id=glmConfig["zh_kimi_prompt_file_id"]
).content.decode()


def Detox_zero_shot(toxic_sentence, neutral_sentence):
    global zh_kimi_prompt_file_content
    response = client.chat.completions.create(
        temperature=0.9,
        model=glmConfig["model"],  # 填写需要调用的模型编码
        messages=[
            # {"role": "user", "content": zh_kimi_prompt_file_content},
            # {
            #     "role": "assistant",
            #     "content": "好的，我已经理解了文本解毒任务。现在我可以进行文本解毒，也可以评估一个模型的文本解毒完成效果。",
            # },
            {
                "role": "user",
                "content": f"""你现在是评估有毒文本释义改写为无毒文本任务的工作人员，你需要评估句子1和句子2：
    指标content_score: 指标content_score指出句子1和句子2的意思是否相同（或接近），这两个句子不必完全相同，其中一个句子可以具有攻击性，而另一个句子则是用中性的语气表达同样的意思。可分为两类：
    （1）content_score=1: 句子1和句子2的意思是否相同（或接近）。
    （2）content_score=0: 句子1和句子2的意思不相同，或者句子1和句子2的意思不相同不接近接近, 或者一个或两个句子都包含无意义的话（非词语、无意义的词语串等）。

    句子1: {toxic_sentence}
    句子2: {neutral_sentence}
    The output should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

    ```json
    {{
    "content_score": number,
    "reason": "string" // 你的理由
    }}
    ```
    你返回的 JSON 结果移除所有的注释，并确保所有的字符串都用双引号括起来。
    """,
            },
        ],
    )
    return extract_json_from_string(response.choices[0].message.content)


# 创建一个空的csv文件，用于实时存储结果
df = pd.DataFrame(
    columns=[
        "toxic_sentence",
        "neutral_sentence",
        "toxic_fluency",
        "content_score",
        "neutral_fluency",
        "toxic_pairwise_score",
        "reason",
    ]
)
df.to_csv("onlycontent-results-zero-shot.csv", index=False)


for index, row in df_c.iterrows():
    data = Detox_zero_shot(row["toxic_sentence"], row["neutral_sentence"])
    # 将结果写入csv文件
    df.loc[index, "toxic_fluency"] = data.get("toxic_fluency")
    df.loc[index, "content_score"] = data.get("content_score")
    df.loc[index, "neutral_fluency"] = data.get("neutral_fluency")
    df.loc[index, "toxic_pairwise_score"] = data.get("toxic_pairwise_score")
    df.loc[index, "reason"] = data.get("reason")
    df.to_csv("onlycontent-results-zero-shot.csv", index=False)
    print(f"第 {index+1} 条数据处理完成啦")
    print(data)
    print("\n")
print("所有数据处理完成！！！")
