from zhipuai import ZhipuAI
import re
import json
import pandas as pd

df_c = pd.read_csv(
    "./data/CM/ZH.tsv",
    sep="\t",
)

with open("config.json", "r") as f:
    config = json.load(f)

glmConfig = config.get("glm")


def extract_json_from_string(input_string):
    # 使用正则表达式匹配 JSON 部分
    json_match = re.search(r"\{.*\}", input_string, re.DOTALL)
    if json_match:
        json_str = json_match.group(0)
        # 将 JSON 字符串中的双引号替换为单引号
        json_str = json_str.replace('""', '"')
        # 将 JSON 字符串转换为字典
        json_dict = json.loads(json_str)
        return json_dict
    else:
        raise ValueError("未找到有效的 JSON 内容")


client = ZhipuAI(api_key=glmConfig["apiKey"])  # 填写您自己的APIKey


def Detox_zero_shot(toxic_sentence, neutral_sentence):
    response = client.chat.completions.create(
        temperature=0.1,
        model=glmConfig["model"],  # 填写需要调用的模型编码
        messages=[
            {
                "role": "user",
                "content": f"""你现在是评估有毒文本释义改写为无毒文本任务的工作人员，你需要评估句子1和句子2：
    指标toxic_pairwise_score：指标toxic_pairwise_score是选择句子1和句子2哪个句子更具攻击性，有下面三类：
    （1）toxic_pairwise_score=1：句子1 更具攻击性。
    （2）toxic_pairwise_score=0：句子2 更具攻击性。
    （3）toxic_pairwise_score=0.5：这两个句子的冒犯性完全相同（例如，两个句子完全中立或完全冒犯）
    指标content_score: 指标content_score指出句子1和句子2的意思是否相同（或接近），这两个句子不必完全相同，其中一个句子可以具有攻击性，而另一个句子则是用中性的语气表达同样的意思。可分为两类：
    （1）content_score=1: 句子1和句子2的意思是否相同（或接近）。
    （2）content_score=0: 句子1和句子2的意思不相同，或者句子1和句子2的意思不相同不接近接近, 或者一个或两个句子都包含无意义的话（非词语、无意义的词语串等）。
    指标fluency：指标fluency可分为三类：
    （1）句子完全正确或有轻微错误，设置 fluency=1。如果句子写得完全正确，用词正确且恰当。此外，我们不处理标点符号错误--我们允许逗号、句号或它们出现在句子的错误位置，设置 fluency=1。我们也会出现词的大小写错误--句子可能以小写字母开头，名字也可能以小写字母书写，或者一个单词可能突然以大写字母开头，设置 fluency=1。只有标点符号错误或大小写错误，设置 fluency=1
    （2）fluency=0.5：句子有错误，但不影响理解。如果文中有拼写错误--单词的形式不恰当、粘连在一起、拼写错误--但仍能完全理解文章的意思。

    （3）fluency=0：句子有一些错误使您无法理解其含义。如果文字歪曲、使用完全不恰当，还有一些奇怪的符号，无法理解全文或部分内容的意思。

    指标toxic_fluency：根据指标fluency，设置toxic_fluency为句子1对应指标fluency的结果

    指标neutral_fluency：根据指标fluency，设置neutral_fluency为句子2对应指标fluency的结果
    句子1: {toxic_sentence}
    句子2: {neutral_sentence}
    The output should be a markdown code snippet formatted in the following schema, including the leading and trailing "```json" and "```":

    ```json
    {{
    "toxic_fluency": number,
    "content_score": number,
    "neutral_fluency": number,
    "toxic_pairwise_score": number,
    "reason": "string" // 你的理由
    }}
    ```
    你返回的 JSON 结果移除所有的注释，并确保所有的字符串都用双引号括起来。
    """,
            }
        ],
    )
    return extract_json_from_string(response.choices[0].message.content)


# 创建一个空的csv文件，用于实时存储结果
df = pd.DataFrame(
    columns=[
        "toxic_sentence",
        "neutral_sentence",
        "toxic_fluency",
        "content_score",
        "neutral_fluency",
        "toxic_pairwise_score",
        "reason",
    ]
)
df.to_csv("results.csv", index=False)


for index, row in df_c.iterrows():
    data = Detox_zero_shot(row["toxic_sentence"], row["neutral_sentence"])
    # 将结果写入csv文件
    df.loc[index, "toxic_fluency"] = data["toxic_fluency"]
    df.loc[index, "content_score"] = data["content_score"]
    df.loc[index, "neutral_fluency"] = data["neutral_fluency"]
    df.loc[index, "toxic_pairwise_score"] = data["toxic_pairwise_score"]
    df.loc[index, "reason"] = data["reason"]
    df.to_csv("results.csv", index=False)
    print(f"第 {index+1} 条数据处理完成啦")
    print(data)
    print("\n")
print("所有数据处理完成！！！")
