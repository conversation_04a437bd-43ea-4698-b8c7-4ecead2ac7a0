# 读取文件A和文件B
# 输出文件A列名为x的列和文件B列名为y的列的皮尔森相关系数
import pandas as pd
import numpy as np

file_A_path = (
    "/Users/<USER>/DetoxScore/detoxScore_for_CM_under_textdetox2024/code/data/CM/ZH.tsv"
)
file_B_path = "/Users/<USER>/DetoxScore/detoxScore_for_CM_under_textdetox2024/code/results-zero-shot.csv"
x = "content_score"
y = "content_score"


def get_pearson_correlation(file_A_path, file_B_path, x, y):
    A = pd.read_csv(file_A_path, sep="\t")
    B = pd.read_csv(file_B_path)
    print(A.columns)
    print(B.columns)
    return np.corrcoef(A[x], B[y])[0, 1]


print(get_pearson_correlation(file_A_path, file_B_path, x, y))
